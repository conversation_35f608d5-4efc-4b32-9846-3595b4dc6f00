-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:45
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-14 14:30:36
local 商城类 = class(窗口逻辑)
local 鼠标弹起 = 引擎.鼠标弹起
local 鼠标按住 = 引擎.鼠标按住
local 取金钱颜色 = 引擎.取金钱颜色
local insert = table.insert
local remove = table.remove
local 向下取整 = math.floor
local 向上取整 = math.ceil
local 返回最小数 = math.min
local 返回最大数 = math.max
local ani = 引擎.取战斗模型
local tp
local x类 = {"组1","组2","组3","组4","组5","组6","组7","组8","组9","组10","宝宝","神兽","仙玉商城"}
function 商城类:初始化(根)
	tp=根
	self.ID = 74
	self.x = 0
	self.y = 0
	self.xx = 0
	self.yy = 0
	self.注释 = "商城类"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.本类开关 = false
	self.加入 = 0
	self.选中编号4=0
	self.选中编号3 = 0
	self.选中编号2 = 0
	self.选中编号 = 0
	self.银两数额 = 0
	self.分类="组1"
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('给予总控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("数量输入",0,0,110,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(2)
	self.输入框:置数字模式()
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)
	self.查看状态=0
	self.介绍文本 = 根._丰富文本(125,125,根.字体表.华康字体)
	文字=根.字体表.一般字体1
end
function 商城类:加载数据(数据)
	self.数据 = 数据
end
function 商城类:打开()
	if self.选中编号 ~=0 then
		return false
	end
	if self.可视 then
		self.可视 = false
		self.选中编号2 = 0
		self.选中编号3 = 0
		self.选中编号4 = 0
		self.选中编号 = 0
		self.介绍文本:清空()
		self.输入框:置焦点(false)
		self.输入框:置可视(false,false)
	else
		self.x = 全局游戏宽度/2-300
		self.y = 全局游戏高度/2-195
		if self.数据==nil then
			return
		end
		insert(tp.窗口_,self)
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
		self.可视 = true

	local 资源 = tp.资源
	local 按钮 = tp._按钮
	local 自适应 = tp._自适应
	self.银两图标=资源:载入('pic/UI12.png',"图片")
	self.仙玉图标=资源:载入('pic/UI11.png',"图片")
	self.按钮坐标={银子商城=53,仙玉商城=53+35*1,神兽商城=53+35*2,宝宝商城=53+35*3}
	self.资源组 = {
    [0] = 自适应.创建(1,1,600-20,18,1,3,nil,18),
	[1]= 自适应.创建(0,1,600,391,3,9),
	[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
	[3] = 自适应.创建(3,1,110,19,1,3),
	[5] = 自适应.创建(5,1,320,320,3,9),
	[6] = 自适应.创建(5,1,155,155,3,9),
	[7] = 自适应.创建(3,1,63,19,1,3),
	[8] =  自适应.创建(6,0,150,58,3,9),
	[11] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,"银子商城"),
	[12] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,"仙玉商城"),
	[13] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,"神兽商城"),
	[14] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,"宝宝商城"),
	[17] = 按钮.创建(自适应.创建(13,4,80,20,1,3),0,0,4,true,true," 上一页"),
	[18] = 按钮.创建(自适应.创建(13,4,80,20,1,3),0,0,4,true,true," 下一页"),
	[19] = 按钮.创建(自适应.创建(13,4,80,20,1,3),0,0,4,true,true,"  购 买"),
	[20] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"药品"),
	[21] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"食物"),
	[22] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"打造"),
	[23] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"宝宝"),
	[24] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"临时"),
	[25] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"宝石"),
	[26] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"杂货"),
	[27] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"卡片"),
	[28] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"其他"),
	[29] = 按钮.创建(自适应.创建(10,4,43,20,1,3),0,0,4,true,true,"法宝"),
	}


		self.分类="组1"
		self.选中编号4=0
		self.选中编号3 = 0
		self.选中编号2 = 0
		self.选中编号 = 0
		self.选中总类="银子商城"
		self.本类开关 = true
		self.组号 = 18
		self.物品数据 = {组1={},组2={},组3={},组4={},组5={},组6={},组7={},组8={},组9={},组10={},宝宝={},神兽={},仙玉商城={}}
		self.商品编号 = 0
		for n = 1,#x类 do
			if x类[n]=="宝宝" or x类[n]=="神兽" then
				for i=1,#self.数据[x类[n]] do
					local xn = 引擎.取头像(self.数据[x类[n]][i].名称)
					self.物品数据[x类[n]][i] = tp.资源:载入(xn[7],"网易WDF动画",xn[1])
					self.物品数据[x类[n]][i].编号 = n
					self.物品数据[x类[n]].数量的=#self.数据[x类[n]]
					self.物品数据[x类[n]][i].价格=self.数据[x类[n]][i].价格
					self.物品数据[x类[n]][i].名称=self.数据[x类[n]][i].名称
				end
			else
				for i=1,#self.数据[x类[n]] do
					self.物品数据[x类[n]][i] = tp._物品格子.创建(0,0,i,"给予_物品")
					self.物品数据[x类[n]][i]:置物品(self.数据[x类[n]][i])
					self.物品数据[x类[n]].数量的=#self.数据[x类[n]]
					self.物品数据[x类[n]][i].编号 = n
					self.物品数据[x类[n]][i].价格=self.数据[x类[n]][i].价格
				end
			end
		end
		self.数量 = 1
		self.价格 = 0
		self.总额 = 0
		self.选中编号2 = 0
		self.选中编号3 = 0
		self.选中编号4 = 0
		self.输入框:置可视(true,true)
		self.输入框:置文本("")
		self.翻页数据 = math.floor(self.物品数据[self.分类].数量的/10)
	end
end
function 商城类:显示(dt,x,y)
	self.焦点=false
    self.资源组[1]:显示(self.x,self.y)
    self.资源组[0]:显示(self.x+3,self.y+3)
    self.资源组[2]:显示(self.x+self.资源组[1].宽度-20,self.y+3)
    tp.窗口标题背景_:置区域(0,0,80,16)
    tp.窗口标题背景_:显示(self.x+600/2-40,self.y+3)
    tp.字体表.华康字体:置颜色(0xFFFFFFFF):显示(self.x+self.资源组[1].宽度/2-tp.字体表.华康字体:取宽度("商 城")/2,self.y+3,"商 城")
	self.资源组[5]:显示(self.x+100,self.y+45)
	self.资源组[6]:显示(self.x+100+320+10,self.y+45)

	self.资源组[2]:更新(x,y)
	for i = 11,14 do
		self.资源组[i]:更新(x,y)
	end
	for i = 17,19 do
		self.资源组[i]:更新(x,y)
	end
	self.资源组[11]:显示(self.x+10,self.y+self.按钮坐标.银子商城)
	self.资源组[13]:显示(self.x+10,self.y+self.按钮坐标.神兽商城)
	self.资源组[14]:显示(self.x+10,self.y+self.按钮坐标.宝宝商城)
	self.资源组[12]:显示(self.x+10,self.y+self.按钮坐标.仙玉商城)
	self.控件类:更新(dt,x,y)
	self.控件类:显示(x,y)
		if self.资源组[11]:事件判断() then
			self.选中总类="银子商城"
			self.分类="组1"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[12]:事件判断() then
			self.选中总类="仙玉商城"
			self.分类="仙玉商城"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[14]:事件判断() then
			self.选中总类="宝宝"
			self.分类="宝宝"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[13]:事件判断() then
			self.选中总类="神兽"
			self.分类="神兽"
			self:初始系统(dt,x,y,2)
			self.加入=0
	elseif self.资源组[2]:事件判断() then
		self:打开()
		self.本类开关 = false
		self.输入框:置可视(false,false)
		return false
	end
	if self.资源组[19]:是否选中(x,y) and 鼠标弹起(左键) then
		if self.商品编号 == 0 then
			tp.常规提示:打开("#y/请先选择一件商品")
		else
			if self.商品编号~=0 then
				if not 判定数字合法(self.输入框:取文本()) then return  引擎.场景.常规提示:打开("请输入正确的数量") end
				if self.分类 == "神兽" then
					self.发送信息 = {
					编号 = self.商品编号,
					位置 = self.商品编号,
					数量 = 1,
					组号 = self.组号,
					分类 = self.分类
					}
					if self.分类 == "神兽" and self.数据[self.分类][self.商品编号] and self.数据[self.分类][self.商品编号].名称 then
						tp.窗口.对话栏:文本("","神兽使者 ","少侠您想购买哪种类型的#G"..self.数据[self.分类][self.商品编号].名称.."#W呢？",{"物理型（单点爆伤）","法术型（秒伤）"})
					end
					return
				elseif self.分类 == "宝宝" then
					self.发送信息 = {
					编号 = self.商品编号,
					位置 = self.商品编号,
					数量 = 1,
					组号 = self.组号,
					分类 = self.分类
					}
					发送数据(30,self.发送信息)
					return
				else
					self.发送信息 = {
					编号 = self.商品编号,
					位置 = self.商品编号,
					数量 = self.输入框:取文本(),
					组号 = self.组号,
					分类 = self.分类
					}
					发送数据(30,self.发送信息)
				end
			end
		end
	end
	if self.选中总类=="银子商城" then
		self.资源组[20]:更新(x,y,self.分类~="组1")
		self.资源组[21]:更新(x,y,self.分类~="组2")
		self.资源组[22]:更新(x,y,self.分类~="组3")
		self.资源组[23]:更新(x,y,self.分类~="组4")
		self.资源组[24]:更新(x,y,self.分类~="组5")
		self.资源组[25]:更新(x,y,self.分类~="组6")
		self.资源组[26]:更新(x,y,self.分类~="组7")
		self.资源组[27]:更新(x,y,self.分类~="组8")
		self.资源组[28]:更新(x,y,self.分类~="组9")
		self.资源组[29]:更新(x,y,self.分类~="组10")
		self.资源组[20]:显示(self.x+97,self.y+22,true,nil,nil,self.分类 == "组1",2)
		self.资源组[21]:显示(self.x+97+49,self.y+22,true,nil,nil,self.分类 == "组2",2)
		self.资源组[22]:显示(self.x+97+49*2,self.y+22,true,nil,nil,self.分类 == "组3",2)
		self.资源组[23]:显示(self.x+97+49*3,self.y+22,true,nil,nil,self.分类 == "组4",2)
		self.资源组[24]:显示(self.x+97+49*4,self.y+22,true,nil,nil,self.分类 == "组5",2)
		self.资源组[25]:显示(self.x+97+49*5,self.y+22,true,nil,nil,self.分类 == "组6",2)
		self.资源组[26]:显示(self.x+97+49*6,self.y+22,true,nil,nil,self.分类 == "组7",2)
		self.资源组[27]:显示(self.x+97+49*7,self.y+22,true,nil,nil,self.分类 == "组8",2)
		self.资源组[28]:显示(self.x+97+49*8,self.y+22,true,nil,nil,self.分类 == "组9",2)
		self.资源组[29]:显示(self.x+97+49*9,self.y+22,true,nil,nil,self.分类 == "组10",2)
		if self.资源组[20]:事件判断() then
			self.分类="组1"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[21]:事件判断() then
			self.分类="组2"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[22]:事件判断() then
			self.分类="组3"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[23]:事件判断() then
			self.分类="组4"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[24]:事件判断() then
			self.分类="组5"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[25]:事件判断() then
			self.分类="组6"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[26]:事件判断() then
			self.分类="组7"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[27]:事件判断() then
			self.分类="组8"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[28]:事件判断() then
			self.分类="组9"
			self:初始系统(dt,x,y,2)
			self.加入=0
		elseif self.资源组[29]:事件判断() then
			self.分类="组10"
			self:初始系统(dt,x,y,2)
			self.加入=0
		end
	end
	if self.分类=="宝宝" or self.分类=="神兽"  then
		self:召唤兽类(dt,x,y)
	else
		self:物品类(dt,x,y)
	end

	self.资源组[3]:显示(self.x+100+320+10+45,self.y+210-3)
	self.资源组[3]:显示(self.x+100+320+10+45,self.y+210+30-3)
	self.资源组[3]:显示(self.x+100+320+10+45,self.y+210+60-3)

	文字:置颜色(白色):显示(self.x+100+320+10,self.y+210,"数量")
	self.输入框:置坐标(self.x+100+320+10+60,self.y+210)
	self.控件类:更新(dt,x,y)
	if self.输入框._已碰撞 then
		self.焦点 = true
	end
	self.控件类:显示(x,y)
	文字:置颜色(白色):显示(self.x+100+320+10,self.y+210+30,"花费")
	文字:置颜色(取金钱颜色(self.总额))
	文字:显示(self.x+100+320+10+60,self.y+210+30,self.总额)
	文字:置颜色(白色):显示(self.x+100+320+10,self.y+210+60,"拥有")

			if self.分类 == "仙玉商城" or self.分类 == "神兽"  then
				self.仙玉图标:显示(self.x+100+320+40-3,self.y+210+60-3)
	文字:置颜色(取金钱颜色(tp.仙玉))
	文字:显示(self.x+100+320+10+60,self.y+210+60,tp.仙玉)
				else
				self.银两图标:显示(self.x+100+320+40-3,self.y+210+60-3)
	文字:置颜色(取金钱颜色(tp.金钱))
	文字:显示(self.x+100+320+10+60,self.y+210+60,tp.金钱)
end
	self.资源组[17]:显示(self.x+100+320+10+40,self.y+210+90)
	self.资源组[18]:显示(self.x+100+320+10+40,self.y+210+120)
	self.资源组[19]:显示(self.x+100+320+10+40,self.y+210+150)

				if self.仙玉图标:是否选中(x,y) then
					tp.提示:自定义(x-42,y+27,"此图标代表物品用仙玉进行结算")
				end
				if self.银两图标:是否选中(x,y) then
					tp.提示:自定义(x-42,y+27,"此图标代表物品用银两进行结算")
				end
end
function 商城类:初始系统(dt,x,y,l)
	if l~=nil and l==2 then
		self.商品编号=0
		self.数量 = 1
		self.价格 = 0
		self.总额 = 0
		self.选中编号=0
		self.选中编号2=0
		self.选中编号3 = 0
		self.选中编号4 = 0
	else
		self.商品编号=0
		self.数量 = 1
		self.价格 = 0
		self.总额 = 0
		self.选中编号=0
		self.选中编号2=0
		self.选中编号3 = 0
		self.选中编号4 = 0
	end
	self.翻页数据 = math.floor(self.物品数据[self.分类].数量的/10)
end
function 商城类:召唤兽类(dt,x,y)
	self.输入框:置可视(true,true)
	tp.字体表.华康字体:置颜色(红色):显示(self.x+365,self.y+370,"当前:"..(self.加入+1).."/"..(self.翻页数据+1))
	tp.字体表.华康字体:置颜色(黄色):显示(self.x+105,self.y+370,"*右键对象查看技能，再次右键切换")
	if self.资源组[17]:是否选中(x,y) and 鼠标弹起(左键) or 引擎.取鼠标滚轮() ==1 then
		self:初始系统(dt,x,y,0)
		if self.加入 >0 then
			self.加入 = self.加入 - 1
		end
	elseif self.资源组[18]:是否选中(x,y) and 鼠标弹起(左键) or 引擎.取鼠标滚轮() ==-1 then
		self:初始系统(dt,x,y,0)
		if self.加入 <self.翻页数据 then
			self.加入 = self.加入 + 1
		end
	end
	self.焦点 = false
	self.选中编号 = 0
	local x横,x竖 = 1,1
	local xx = 0
	local yy = 0
	local 偏移x,偏移y = 103,28
	local 最大显示数 = math.min(10, #self.物品数据[self.分类] - self.加入*10)
	for n = 1, 最大显示数 do
		if self.物品数据[self.分类][n+self.加入*10]~=nil then
			if self.物品数据[self.分类][n+self.加入*10]:是否选中(x,y) then
				self.选中编号 = n+self.加入*10
				if 引擎.鼠标弹起(左键) then
					self.选中编号2=n
					self.选中编号3 = n+self.加入*10
					self.选中编号4 = n+self.加入*10
				end
			end
			self.资源组[8]:显示(self.x+偏移x + xx * 150 + 15-7,self.y+偏移y + yy * 60 +30-2)
			self.资源组[7]:显示(self.x+偏移x + xx * 150 + 98-15,self.y+偏移y + yy * 60 +60)
			if 全局界面风格=="国韵" then
				tp.宠物头像背景_:显示(self.x+偏移x+10 + xx * 150 + 15-5-16,self.y+偏移y+1 + yy * 60 +30)
			else
				tp.宠物头像背景_:显示(self.x+偏移x+5 + xx * 150 + 15-5,self.y+偏移y+3 + yy * 60 +30)
			end
            self.物品数据[self.分类][n+self.加入*10]:更新(dt)
            self.物品数据[self.分类][n+self.加入*10]:显示(self.x+偏移x+6 + xx * 150 + 18-5,self.y+偏移y+6 + yy * 60 +30)
			文字:置颜色(引擎.取摊位金钱颜色(self.物品数据[self.分类][n+self.加入*10].价格)):显示(self.x+偏移x + xx * 150 + 103-15,self.y+偏移y + yy * 60 +64,self.物品数据[self.分类][n+self.加入*10].价格)
			文字:置颜色(白色):显示(self.x+偏移x + xx * 150 + 71-5,self.y+偏移y + yy * 60 +34,self.物品数据[self.分类][n+self.加入*10].名称)
			if self.分类 == "神兽" then
				self.仙玉图标:显示(self.x+偏移x + xx * 150 + 75-8-5,self.y+偏移y + yy * 60 +64-4)
			else
				self.银两图标:显示(self.x+偏移x + xx * 150 + 75-8-8,self.y+偏移y + yy * 60 +64-4)
			end
			-- 显示装饰图标
			if self.数据[self.分类][n+self.加入*10].装饰 ~= nil then
				self.资源组[30]:显示(self.x+偏移x + xx * 150 + 15,self.y+偏移y + yy * 60 +64)
				if self.资源组[30]:是否选中(x,y) then
					tp.提示:自定义(x-42,y+27,"#Y装饰：#G"..self.数据[self.分类][n+self.加入*10].装饰)
				end
			end
			if 鼠标弹起(右键) and self.物品数据[self.分类][n+self.加入*10]:是否选中(x,y) then
				self.查看状态=self.查看状态+1
				if self.分类=="神兽" and self.查看状态==1 then
					tp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],"物理",true)
				elseif self.分类=="神兽" and self.查看状态==2 then
					tp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],"法系",true)
				else
					tp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],"宝宝")
				end
				if self.查看状态>=2 then
					self.查看状态=0
				end
			end
			if self.选中编号2 ~= 0 and self.选中编号2 == n then
				tp.物品格子焦点_:显示(self.x+偏移x + xx * 150 + 15-3,self.y+偏移y + yy * 60 + 31)
			end

			xx = xx + 1
			if xx == 2 then
				xx = 0
				yy = yy + 1
			end
		end
	end
		if self.动画 then
			tp.影子:显示(self.x+505,self.y+170)
			self.动画:更新()
			self.动画:显示(self.x+505,self.y+170)
			if self.资源组[30] then
            	self.资源组[30]:显示(self.x + 505, self.y + 170)
        	end
		end

	if self.选中编号 ~= 0 then
		if 引擎.鼠标弹起(左键) and self.选中编号3~=0 then
			self:置形象()
			self.价格 = self.物品数据[self.分类][self.选中编号3].价格
			if self.商品编号 == self.选中编号 then
				self.数量 = 1
				self.总额 = self.数量 * self.价格
				self.输入框:置文本(self.数量)
				return 0
			else
				self.数量 = 1
				self.总额 = self.数量 * self.价格
				self.输入框:置文本(self.数量)
			end
			self.商品编号 = self.选中编号
		elseif 引擎.鼠标弹起(右键) and self.商品编号 == self.选中编号 and self.数量 > 1 then
			self.数量 = self.数量 - 1
			self.总额 = self.数量 * self.价格
			self.输入框:置文本(self.数量)
		end
	end
end


function 商城类:置形象()
    self.动画 = {}
    self.资源组[30] = nil  -- 重置装饰动画
    if self.物品数据[self.分类][self.选中编号3] ~= nil then
        local xn = ani(self.物品数据[self.分类][self.选中编号3].名称)
        self.动画 = tp.资源:载入(xn[10], "网易WDF动画", xn[6])
        if ani(self.数据[self.分类][self.选中编号3].名称.."_装饰") ~= nil and #ani(self.数据[self.分类][self.选中编号3].名称.."_装饰") > 0 then
            xn = ani(self.数据[self.分类][self.选中编号3].名称.."_装饰")
            self.资源组[30] = tp.资源:载入(xn[10], "网易WDF动画", xn[6])
        end
        -- if ani(self.数据[self.分类][self.选中编号3].名称.."_身体") ~= nil and #ani(self.数据[self.分类][self.选中编号3].名称.."_身体") > 0 then
        --     xn = ani(self.数据[self.分类][self.选中编号3].名称.."_身体")
        --     self.资源组[30] = tp.资源:载入(xn[10], "网易WDF动画", xn[6])
        -- end           
        self.动画:置方向(0)
    end
end

function 商城类:物品类(dt,x,y)
	self.输入框:置可视(true,true)
	tp.字体表.华康字体:置颜色(红色):显示(self.x+365,self.y+370,"当前:"..(self.加入+1).."/"..(self.翻页数据+1))
	tp.字体表.华康字体:置颜色(黄色):显示(self.x+105,self.y+370,"*使用滚轮可快速翻页查看")
	if self.资源组[17]:是否选中(x,y) and 鼠标弹起(左键) or 引擎.取鼠标滚轮() ==1 then
		self:初始系统(dt,x,y,0)
		if self.加入 >0 then
			self.加入 = self.加入 - 1
			self.选中编号2 = 0  -- 重置选中状态
        	self.选中编号3 = 0
		end
	elseif self.资源组[18]:是否选中(x,y) and 鼠标弹起(左键) or 引擎.取鼠标滚轮() ==-1 then
		self:初始系统(dt,x,y,0)
		if self.加入 <self.翻页数据 then
			self.加入 = self.加入 + 1
			self.选中编号2 = 0  -- 重置选中状态
        	self.选中编号3 = 0
		end
	end
	self.焦点 = false
	self.选中编号 = 0
	local x横,x竖 = 1,1
	local xx = 0
	local yy = 0
	local 偏移x,偏移y = 103,28
	for n = 1, 10 do
		if self.物品数据[self.分类][n+self.加入*10]~=nil then
			if self.物品数据[self.分类][n+self.加入*10].焦点 then
				self.选中编号 = n+self.加入*10
				if 引擎.鼠标弹起(左键) then
					self.选中编号2 = n + self.加入*10
					self.选中编号3 = n+self.加入*10
				end
			end
			self.资源组[8]:显示(self.x+偏移x + xx * 150 + 15-7,self.y+偏移y + yy * 60 +30-2)
			self.资源组[7]:显示(self.x+偏移x + xx * 150 + 98-15,self.y+偏移y + yy * 60 +60)
			tp.物品格子背景_:显示(self.x+偏移x + xx * 150 + 15-5,self.y+偏移y + yy * 60 +30)
			self.物品数据[self.分类][n+self.加入*10]:置坐标(self.x+偏移x + xx * 150 + 18-5,self.y+偏移y + yy * 60 +30)
			self.物品数据[self.分类][n+self.加入*10]:显示(dt,x,y,self.鼠标,nil,nil,nil,100)
			文字:置颜色(引擎.取摊位金钱颜色(self.物品数据[self.分类][n+self.加入*10].价格)):显示(self.x+偏移x + xx * 150 + 103-15,self.y+偏移y + yy * 60 +64,self.物品数据[self.分类][n+self.加入*10].价格)
			文字:置颜色(白色):显示(self.x+偏移x + xx * 150 + 71-5,self.y+偏移y + yy * 60 +34,self.物品数据[self.分类][n+self.加入*10].物品.名称)
			if self.选中编号2 ~= 0 and self.选中编号2 == n + self.加入*10 then
		        tp.物品格子焦点_:显示(self.x+偏移x + xx * 150 + 15-3,self.y+偏移y + yy * 60 + 31)
		        self.介绍文本:清空()
		        self.介绍文本:添加文本("#Y/     "..self.物品数据[self.分类][self.选中编号2].物品.名称)
		        self.介绍文本:添加文本("#H/"..引擎.取物品(self.物品数据[self.分类][self.选中编号2].物品.名称)[1])
		        self.介绍文本:显示(self.x + 445,self.y + 58)
		    end
			if self.分类 == "仙玉商城" then
				self.仙玉图标:显示(self.x+偏移x + xx * 150 + 75-8-4,self.y+偏移y + yy * 60 +64-4)
			else
				self.银两图标:显示(self.x+偏移x + xx * 150 + 75-8-4,self.y+偏移y + yy * 60 +64-4)
			end
		end
			xx = xx + 1
			if xx == 2 then
				xx = 0
				yy = yy + 1
			end
	end
	if self.选中编号 ~= 0 then
		tp.提示:道具行囊(x,y,self.物品数据[self.分类][self.选中编号].物品)
		if 引擎.鼠标弹起(左键) then
			self.价格 = self.数据[self.分类][self.选中编号].价格
			if self.商品编号 == self.选中编号 then
				self.数量 = self.数量 + 1
				self.总额 = self.数量 * self.价格
				self.输入框:置文本(self.数量)
				return 0
			else
				self.数量 = 1
				self.总额 = self.数量 * self.价格
				self.输入框:置文本(self.数量)
			end
			self.商品编号 = self.选中编号
		elseif 引擎.鼠标弹起(右键) and self.商品编号 == self.选中编号 and self.数量 > 1 then
			self.数量 = self.数量 - 1
			self.总额 = self.数量 * self.价格
			self.输入框:置文本(self.数量)
		end
	end
	if self.输入框._已碰撞 then
		self.焦点 = true
	end
end
return 商城类
