{"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 3938, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[1, 1, "revert", null, "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", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvw"], [2, 1, "revert", null, "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", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvw"], [5, 1, "insert", {"characters": "01"}, "AwAAAMIBAAAAAAAAwwEAAAAAAAAAAAAAwwEAAAAAAADDAQAAAAAAAAIAAAA2McMBAAAAAAAAxAEAAAAAAAAAAAAA", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAMIBAAAAAAAAxAEAAAAAAAAAAAAAAADwvw"], [7, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-06 23:49:06"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDMgMjM6MTY6MDY", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAMQBAAAAAAAAxAEAAAAAAAAAAAAAAADwvw"], [3, 1, "left_delete", null, "AQAAACgNAAAAAAAAKA0AAAAAAAAEAAAAICAtLQ", "GgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAsDQAAAAAAACgNAAAAAAAAAAAAAAAA8L8"], [6, 1, "left_delete", null, "AQAAAEMNAAAAAAAAQw0AAAAAAAAEAAAAIC0tIA", "GgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABHDQAAAAAAAEMNAAAAAAAAAAAAAAAA8L8"], [8, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-08 22:01:59"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDYgMjM6NDk6MDY", "GgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABDDQAAAAAAAEMNAAAAAAAAAAAAAAAA8L8"], [13, 1, "toggle_comment", {"block": false}, "AgAAAEMNAAAAAAAARg0AAAAAAAAAAAAAKQ0AAAAAAAAsDQAAAAAAAAAAAAA", "GgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABZDQAAAAAAACgNAAAAAAAAAAAAAAAA8L8"], [16, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-08 22:03:44"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDggMjI6MDE6NTk", "GgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABEDQAAAAAAAEQNAAAAAAAAAAAAAAAA8L8"], [8, 1, "paste", null, "AgAAAHcDAAAAAAAAfAMAAAAAAAAAAAAAfAMAAAAAAAB8AwAAAAAAAAYAAADnvJPlhrI", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAeQMAAAAAAAB3AwAAAAAAAAAAAAAAAPC/"], [13, 1, "paste", null, "AQAAAH8DAAAAAAAA5AMAAAAAAAAAAAAA", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAfwMAAAAAAAB/AwAAAAAAAAAAAAAAAPC/"], [14, 1, "add_file_header", {"path": "D:\\myfwd\\客户端\\main.lua"}, "AQAAAAAAAAAAAAAAkgAAAAAAAAAAAAAA", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAA5AMAAAAAAADkAwAAAAAAAAAAAAAAAPC/"], [15, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 21:12:17"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDggMjI6MDM6NDQ", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAdgQAAAAAAAB2BAAAAAAAAAAAAAAAAPC/"], [20, 1, "cut", {"event": {"modifier_keys": {}, "text_point": 1086, "x": 426.5, "y": 159.5}}, "AQAAABEEAAAAAAAAEQQAAAAAAACjAAAA6LWE5rqQ57yT5a2YID0gcmVxdWlyZSgiU2NyaXB0L+i1hOa6kOexuy/mmbrog73nvJPlrZjns7vnu58iKSh7CiAgICDmnIDlpKfnvJPlrZjmlbAgPSAxNTAwLAogICAg5ZCv55So6aKE5Yqg6L29ID0gdHJ1ZSwKICAgIOiwg+ivleaooeW8jyA9IOiwg+ivleaooeW8jyBvciBmYWxzZQp9KQ", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAdgQAAAAAAAARBAAAAAAAAAAAAAAAAPC/"], [46, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 22:01:29"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMTMgMjE6MTI6MTc", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAA6gcAAAAAAADqBwAAAAAAAAAAAAAAAPC/"], [51, 1, "left_delete", null, "AQAAAAkEAAAAAAAACQQAAAAAAAAJAAAA5LyY5YyW54mI", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAADAQAAAAAAAAJBAAAAAAAAAAAAAAAAPC/"], [53, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 22:05:59"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMTMgMjI6MDE6Mjk", "BAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAACQQAAAAAAAAJBAAAAAAAAAAAAAAAAPC/"]]}, {"file": "Script/显示类/道具详情.lua", "settings": {"buffer_size": 92909, "encoding": "UTF-8", "line_ending": "Windows"}}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"]], ["Packages/Lua/ggegame.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "in", "selected_items": [["in", "Package Control: Install Package"], ["Terminus: Open Terminal", "Terminus: Toggle Panel"], ["Package Control: Install Package", "Package Control: Install Package"], ["Install Package Control", "Package Control: Install Package"], ["Package Control", "Package Control: Install Package"], ["preferences control", "Preferences: Package Control Settings – <PERSON><PERSON>ult"], ["pac", "Package Control: Install Package"], ["", "AutoFileName: <PERSON><PERSON><PERSON>s"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 510.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/客户端", "/D/myfwd/客户端/<PERSON><PERSON>t", "/D/myfwd/客户端/<PERSON><PERSON>t/属性控制", "/D/myfwd/客户端/Script/数据中心", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类"], "file_history": ["/D/myfwd/客户端/<PERSON><PERSON>t/显示类/技能.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/客户端/script/属性控制/队伍.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/智能缓存系统.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/缓冲.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/无边框启动器.lua", "/D/myfwd/客户端/script/战斗类/战斗类.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/帮派界面.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/script/显示类/道具详情.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/生死劫.lua", "/D/mymh/客户端/<PERSON><PERSON>t/系统类/人物框.lua", "/D/新建文件夹/<PERSON>ript/小九UI/图鉴.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/玩家信息.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/Script/数据中心/物品库.lua", "/D/myfwd/客户端/Script/数据中心/传送表.lua", "/D/myfwd/客户端/script/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/myfwd/客户端/<PERSON>ript/显示类/游戏公告类.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/藏宝阁/藏宝阁类.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/script/全局/变量1.lua", "/D/myfwd/客户端/script/小九UI/商业/摊位购买.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/商城类.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/道具行囊.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/更新内容.lua", "/C/Users/<USER>/Documents/Tencent Files/308537402/FileRecv/更新内容.lua", "/C/Users/<USER>/Desktop/修复备份/5.9/客户端/Script/初系统/缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/更新类/鉴定提示.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/鉴定.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/黑市拍卖.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/客户端/script/小九UI/商业/商店.lua", "/D/myfwd/客户端/Script/数据中心/场景NPC.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青_测试.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青_示例.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/Script/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗回放集成.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/Script/初系统/Windows_API_无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/简化版无边框启动器.lua", "/D/myfwd/客户端/Script/数据中心/场景.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/小九UI/角色/人物状态栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/修炼升级.lua", "/D/myfwd/客户端/Script/数据中心/梦战造型.lua", "/D/myfwd/客户端/script/小九UI/排行榜.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/家园访问.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/Script/数据中心/头像库.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/myfwd/客户端/Script/数据中心/坐骑库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽属性栏.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/五子棋.lua", "/D/myfwd/客户端/Script/全局/人物.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/小九UI/道具行囊.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/成就系统.lua", "/D/myfwd/客户端/script/小九UI/角色/成就系统.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/猜拳命令类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/长安保卫战.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/快捷技能栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽资质栏.lua", "/D/myfwd/客户端/<PERSON>ript/显示类/道具详情.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/多重对话类/对话栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/动画类.lua", "/D/myfwd/客户端/<PERSON>ript/资源类/动画类_X9.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/丰富文本.lua", "/D/myfwd/客户端/script/小九UI/翰墨丹青.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘 - 副本.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/攻略查看.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/梦幻指引.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/script/战斗类/战斗单位类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/自适应.lua", "/D/myfwd/客户端/<PERSON>ript/数据中心/普通模型库.lua", "/C/Users/<USER>/Desktop/修复备份/5.24/客户端/Script/战斗类/战斗单位类.lua", "/D/myfwd/服务端/地图障碍设置.lua", "/C/Users/<USER>/Desktop/修复备份/5.24/服务端/Script/对话处理类/NPC对话内容.lua", "/D/myfwd/客户端/update.bat", "/C/Users/<USER>/Desktop/修复备份/4.14/客户端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/召唤兽.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/网络/消息.lua", "/D/myfwd/客户端/<PERSON>ript/全局/玩家.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/网络/数据交换.lua", "/D/myfwd/客户端/script/全局/人物.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/全局/玩家.lua", "/D/myfwd/客户端/script/全局/假人.lua", "/D/myfwd/客户端/script/网络/hp.lua", "/D/myfwd/客户端/<PERSON>ript/网络/消息.lua", "/D/myfwd/GGE/Core/Game/gge纹理类.lua", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/D/myfwd/客户端/Script/main.lua", "/D/myfwd/GGE/Core/Game/gge文字类.lua", "/D/myfwd/客户端/script/系统类/丰富文本.lua", "/D/myfwd/客户端/script/资源类/MAP.lua", "/D/myfwd/GGE/Core/Game/ggecommon.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/D/myfwd/客户端/script/资源类/地图类.lua", "/C/Users/<USER>/Desktop/修复备份/5.9/客户端/<PERSON>ript/小九UI/内置管理工具.lua", "/C/Users/<USER>/Desktop/五子棋 - 副本.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友/好友查看.lua", "/D/myfwd/客户端/Script/数据中心/特效库.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/勾魂索一.lua", "/D/myfwd/客户端/script/系统类/小型选项栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框外部.lua", "/D/myfwd/客户端/<PERSON>ript/聊天框系统/聊天框丰富文本.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/队伍/队伍栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/助战系统.lua"], "find": {"height": 31.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\客户端\\Script,<project filters>", "D:\\mymh\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端", "D:\\myfwd\\客户端\\Script", "D:\\myfwd\\客户端", "D:\\myfwd\\Client", "D:\\myfwd\\Server\\Script,<project filters>,D:\\myfwd\\Client\\Script", "D:\\myfwd\\Server\\Script,<project filters>"]}, "find_state": {"case_sensitive": true, "find_history": ["if self.师门技能[id] ~= nil and self.师门技能[id].包含技能 ", "获取状态", "缓冲", "检测系统配置", "低配", "print", "龙卷", "龙卷雨击", "法术抖动", "抖动", "尸腐", "延迟", "百鬼噬魂", "[6]", "统计", "帮派", "剧情npc", "渡劫剧情", "生死劫", "1001", "人物框", "9000", "[10]", "观察", "不足无法兑换", "商品帽子", "跑商商品", "跑商武器", "跑商帽子", "跑商面粉", "少女的手镯", "福隆当铺", "346", "当铺", "长安民居2", "1036", "1130", "主线", "考古", "缓存", "经典", "画面风格", "切换", "聊天框", "[2]", "余额", "藏宝阁", "print", "窗口恢复正常", "txk", "全局商城购买状态", "商城", "道具行囊", "剧情技能", "标题", "[2]", "[0]", "[1]", "道具行囊", "鉴定提示", "gb", "专用提示", "恭喜！", "古董", "古荡", "妙手", "变化之术", "古董", "变化之术", "古董拍卖", "股东拍卖", "自动", "[1]", "鉴定装备", "你的商店不足", "货商", "苏大娘", "奸商", "AI Assistant", "添加地图", "添加npc", "慧静", "正在启动", "已经扫描", "战神山", "pr", "gge研究", "[210]", "人物排行", "帮派", "人物属性", "免资材", "免紫菜", "丫鬟", "佣人", "[8]", "访问玩家", "1028", "小龙女", "进阶龙鲤", "print", "pr", "非跑商商品", "提示", "无法出售", "字体素材", "[3]", "操作说明", "[4]", " [4]", "进度", "提交作品", "进度", "[5]", "查看说明", "进度", "原字", "当前字体", "当前", "普通字体", "操作说明", "[7]", "[6]", "引导模式", "进度", "已加锁，无法使用", "显示坐骑", "显示变身卡造型"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["虞悄悄", "--print", "item.wdf", "zts:显示(self.x+243+100,self.y+68,打造说明[self.分类标识][self.功能标识])", "zts:显示(self.x+177+100,self.y+68,\"需要材料：\")", "zts:显示(self.x+177+144,self.y+68,\"需要材料：\")", "zts:显示(self.x+243+144,self.y+68,打造说明[self.分类标识][self.功能标识])", "战斗类", "玩家", "武器", "行为", "玩家", "资源", "common/zuoqiyws.wdf", "特效宝珠", "shape.wdf", "shape.wd5", "shape.wd4", "祥瑞坐骑.wdf", "shape.wdf", "common/shape.wdf", "", "pgs_1", "shape.wdf", "pgs_1", "shape.wdf", "", "y", "资源", "（过去）", "（未来）", "", "diedai.wdf", "", "资源", "\"wzife.wdf\"", "资源", "载入('wzife.wdf',\"网易WDF动画\",0X140BBA9),"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 3938, "regions": {}, "selection": [[1033, 1033]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 6, 5, 17, 59, 44, 9, 143, 146, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 858.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Script/显示类/道具详情.lua", "selected": true, "semi_transient": true, "settings": {"buffer_size": 92909, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 6, 5, 17, 59, 45, 1, 187, 213, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 31.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.Terminus": {"height": 150.0}, "output.exec": {"height": 117.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "project": "开发客户端.sublime-project", "replace": {"height": 58.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["战斗命令", "Client\\Script\\战斗类\\战斗命令类.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 282.0, "status_bar_visible": true, "template_settings": {}}