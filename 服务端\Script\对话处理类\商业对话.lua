-- @Author: baidwwy
-- @Date:   2024-11-29 20:34:53
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-14 14:41:17
-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:44
-- @Last Modified by:   虞悄悄 - QQ：308537402
-- @Last Modified time: 2024-05-19 16:38:17
local 场景类_对话商业栏 = class()
local sj = 取随机数
local format = string.format
local insert = table.insert
local ceil = math.ceil
local floor = math.floor
local wps = 取物品数据
local typ = type
local random = 取随机数
local 五行_ = {"金","木","水","火","土"}
function 场景类_对话商业栏:初始化() end
function 场景类_对话商业栏:购买商品(连接id,id,序号,内容)
	-- 检查古董商人状态污染问题
	if 玩家数据[id] and 玩家数据[id].商店编号 == 106 then
		-- 检查商品列表是否为古董商人商品（通过商品格式判断）
		local 是古董商人商品 = false
		if 玩家数据[id].商品列表 and type(玩家数据[id].商品列表) == "table" and #玩家数据[id].商品列表 > 0 then
			local 第一个商品 = 玩家数据[id].商品列表[1]
			if 第一个商品 and (第一个商品:find("金柳露") or 第一个商品:find("超级金柳露") or 第一个商品:find("清灵净瓶")) then
				是古董商人商品 = true
			end
		end
		
		-- 如果不是古董商人商品或古董商人系统不可用，清理状态
		if not 是古董商人商品 or not 古董商人 or type(古董商人.处理购买请求) ~= "function" then
			玩家数据[id].商店编号 = nil
			-- 继续下面的正常商店购买流程
		else
			-- 确实是古董商人购买请求，调用古董商人处理逻辑
			local 商品名称 = 内容.商品
			local 数量 = 内容.数量 or 1
			local 购买结果 = 古董商人:处理购买请求(id, 商品名称, 数量)
			
			-- 如果古董商人处理成功，直接返回
			if 购买结果 then
				return
			else
				-- 古董商人处理失败，清理状态并继续正常流程
				玩家数据[id].商店编号 = nil
				玩家数据[id].商品列表 = nil
				-- 继续下面的正常商店购买流程
			end
		end
	end
	
	self.商品匹配=false
	if type(玩家数据[id].商品列表)~="table" then
		异常账号(id,"未触发商品界面却触发购买请求。请求购买的商品数据为："..内容.商品)
		return 0
	end
	self.组合商品=""
	for n=1,#玩家数据[id].商品列表 do
		self.组合商品=self.组合商品..玩家数据[id].商品列表[n]
		if 玩家数据[id].商品列表[n]==内容.商品 then
			self.商品匹配=true
			break
		end
	end
	if self.商品匹配==false then
		异常账号(id,"所购买的商品不再列表之类。商品列表为"..self.组合商品.."购买商品为"..内容.商品)
		return 0
	end
	内容.数量 = 内容.数量+0
	local 道具格子=玩家数据[id].角色:取道具格子()
	if 道具格子==0 then
		常规提示(id,"您的道具栏物品已经满啦")
		return 0
	end
	local 商品分割=分割文本(内容.商品,"*")
	local 商品名称=商品分割[1]
	local 商品单价=商品分割[2]
	local 商品附加=商品分割[3]
	local 商品数量=内容.数量
	local 商品类型=内容.类型
	local 商品道具=物品类()
	商品道具:置对象(商品名称)
	商品数量=qz(商品数量)
	商品单价=qz(商品单价)
	if 商品数量<1 or 商品单价<1 then
		return 0
	end
	if 商品类型 == "银子" then
		self:购买商品银子(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	elseif 商品类型 == "秘制积分" then
		self:购买商品秘制积分(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	elseif 商品类型 == "帮贡" then
		self:购买商品帮贡(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	else
		self:购买商品积分(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加,商品类型)
	end
end
function 场景类_对话商业栏:购买商品银子(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	local 购买成功 = false
	local 道具格子=0
	if 取银子(id)< 商品数量*商品单价 then
		商品数量 = qz(取银子(id)/商品单价)
	end
	if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then
		if 商品数量 > 0 then
			if 玩家数据[id].角色:取空道具格子数量() < 商品数量 then
				商品数量 = 玩家数据[id].角色:取空道具格子数量()
			end
			if 商品数量 > 0 then
				for n=1,商品数量 do
					道具格子=玩家数据[id].角色:取道具格子()
					if 道具格子==0 then
						常规提示(id,"您的道具栏物品已经满啦")
						return 0
					end
					if 商品名称=="高级魔兽要诀" then
						玩家数据[id].道具:给予道具(id,"高级魔兽要诀",nil,nil,"商店")
					elseif 商品名称=="灵饰指南书" or 商品名称=="随机灵饰指南书" then
						玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10},nil,"商店")
					elseif 商品名称=="元灵晶石" or 商品名称=="随机元灵晶石" then
						玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10},nil,"商店")
					elseif 商品名称=="制造指南书" then
						玩家数据[id].道具:给予道具(id,"制造指南书",{9,10,11,12},nil,"商店")
					elseif 商品名称=="百炼精铁" then
						玩家数据[id].道具:给予道具(id,"百炼精铁",{9,10,11,12},nil,"商店")
					elseif 商品名称=="上古锻造图策" then
						玩家数据[id].道具:给予道具(id,"上古锻造图策",{7,8,9},nil,"商店")
					elseif 商品名称=="炼妖石" then
						玩家数据[id].道具:给予道具(id,"炼妖石",{7,8,9},nil,"商店")
					elseif 商品名称=="随机怪物卡片" then
						local lv=取随机数(1,8)
						玩家数据[id].道具:给予道具(id,"怪物卡片",lv,nil,"商店")
					elseif 商品名称=="战魄" then
						玩家数据[id].道具:给予道具(id,"战魄",商品附加+0,nil,"商店")
					elseif 商品名称=="陨铁" then
						玩家数据[id].道具:给予道具(id,"陨铁",商品附加+0,nil,"商店")
					elseif 商品名称=="珍珠" then
						玩家数据[id].道具:给予道具(id,"珍珠",商品附加+0,nil,"商店")
					else
						if 商品名称=="星辉石" or 商品名称=="舍利子" or 商品名称=="红玛瑙" or 商品名称=="黑宝石" or 商品名称=="光芒石"  or 商品名称=="月亮石"or 商品名称=="太阳石" or 商品名称=="精魄灵石" then
							local lv=商品附加
							if lv then
								lv=lv+0
							else
								lv=1
							end
							玩家数据[id].道具:给予道具(id,商品名称,lv,nil,"商店")
						else
							玩家数据[id].道具:给予道具(id,商品名称,nil,nil,"商店")
						end
					end
				end
				购买成功 = true
			else
				常规提示(id,"您的道具栏物品已经满啦")
			end
		else
			常规提示(id,"你的银子不足无法购买")
		end
	else
		道具格子=玩家数据[id].角色:取道具格子()
		if 道具格子==0 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		if 商品数量 > 0 then
			if 商品名称=="高级魔兽要诀" then
				玩家数据[id].道具:给予道具(id,"高级魔兽要诀",nil,nil,"商店")
			elseif 商品名称=="灵饰指南书" or 商品名称=="随机灵饰指南书" then
				玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10},nil,"商店")
			elseif 商品名称=="元灵晶石" or 商品名称=="随机元灵晶石" then
				玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10},nil,"商店")
			elseif 商品名称=="制造指南书" then
				玩家数据[id].道具:给予道具(id,"制造指南书",{9,10,11,12},nil,"商店")
			elseif 商品名称=="百炼精铁" then
				玩家数据[id].道具:给予道具(id,"百炼精铁",{9,10,11,12},nil,"商店")
			elseif 商品名称=="上古锻造图策" then
				玩家数据[id].道具:给予道具(id,"上古锻造图策",{7,8,9},nil,"商店")
			elseif 商品名称=="炼妖石" then
				玩家数据[id].道具:给予道具(id,"炼妖石",{7,8,9},nil,"商店")
			elseif 商品名称=="随机怪物卡片" then
				local lv=取随机数(1,8)
				玩家数据[id].道具:给予道具(id,"怪物卡片",lv,nil,"商店")
			elseif 商品名称=="战魄" then
				玩家数据[id].道具:给予道具(id,"战魄",商品附加+0,nil,"商店")
			elseif 商品名称=="珍珠" then
				玩家数据[id].道具:给予道具(id,"珍珠",商品附加+0,nil,"商店")
			else
				-- 对于不可叠加物品，需要逐个给予
				for i = 1, 商品数量 do
					玩家数据[id].道具:给予道具(id,商品名称,1,nil,"商店")
				end
			end
			购买成功 = true
		else
			常规提示(id,"你的银子不足无法购买")
		end
	end
	if 购买成功 then
		local 总价格=商品数量*商品单价
		玩家数据[id].角色:扣除银子(总价格,0,0,"购买系统商店商品["..商品名称.."]")
		发送数据(玩家数据[id].连接id,3520,{银子=玩家数据[id].角色.银子})
		发送数据(玩家数据[id].连接id,38,{内容="#W您花费#R"..总价格.."#W两银子购买了#R"..商品数量.."#W个#R"..商品名称,频道="xx"})
		-- 使用正确的道具刷新方法，发送完整的道具数据
		玩家数据[id].道具:索要道具(玩家数据[id].连接id,id)
	end
end
function 场景类_对话商业栏:购买商品积分(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加,商品类型)
	local 购买成功 = false
	local 道具格子=0
	local 积分=0
	if 商品类型=="副本积分" then
		积分=玩家数据[id].角色.副本积分
	elseif 商品类型=="钓鱼积分" then
		积分=玩家数据[id].角色.钓鱼积分
	elseif 商品类型=="中秋积分" then
		积分=玩家数据[id].角色.中秋积分
	elseif 商品类型=="小龟积分" then
		积分=玩家数据[id].角色.小龟积分
	elseif 商品类型=="门派贡献" then
		积分=玩家数据[id].角色.门派
	elseif 商品类型=="活动积分" then
		积分=玩家数据[id].角色.活动积分
	elseif 商品类型=="神器积分" then
		积分=玩家数据[id].角色.神器积分		
	end
	if 积分< 商品数量*商品单价 then
		商品数量 = qz(积分/商品单价)
	end
	if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then
		if 商品数量 > 0 then
			if 玩家数据[id].角色:取空道具格子数量() < 商品数量 then
				商品数量 = 玩家数据[id].角色:取空道具格子数量()
			end
			if 商品数量 > 0 then
				for n=1,商品数量 do
					道具格子=玩家数据[id].角色:取道具格子()
					if 道具格子==0 then
						常规提示(id,"您的道具栏物品已经满啦")
						return 0
					end
					if 商品名称=="召唤兽内丹" then
						玩家数据[id].道具:给予道具(id,"召唤兽内丹",nil,取内丹("低级"),"商店")
					elseif 商品名称=="高级召唤兽内丹" then
						玩家数据[id].道具:给予道具(id,"高级召唤兽内丹",nil,取内丹("高级"),"商店")
					elseif 商品名称=="高级魔兽要诀" then
						玩家数据[id].道具:给予道具(id,"高级魔兽要诀",nil,nil,"商店")
					elseif 商品名称=="灵饰指南书" or 商品名称=="随机灵饰指南书" then
						玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10},nil,"商店")
					elseif 商品名称=="元灵晶石" or 商品名称=="随机元灵晶石" then
						玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10},nil,"商店")
					elseif 商品名称=="随机怪物卡片" then
						local lv=取随机数(1,8)	
						玩家数据[id].道具:给予道具(id,"怪物卡片",lv,nil,"商店")					
					else
						if 商品名称=="星辉石" or 商品名称=="舍利子" or 商品名称=="红玛瑙" or 商品名称=="黑宝石" or 商品名称=="光芒石"  or 商品名称=="月亮石"or 商品名称=="太阳石" or 商品名称=="精魄灵石" then
							local lv=商品附加
							if lv then
								lv=lv+0
							else
								lv=1
							end
							玩家数据[id].道具:给予道具(id,商品名称,lv,nil,"商店")
						else
							玩家数据[id].道具:给予道具(id,商品名称,nil,nil,"商店")
						end
					end
				end
				购买成功 = true
			else
				常规提示(id,"您的道具栏物品已经满啦")
			end
		else
			常规提示(id,"你的"..商品类型.."不足无法兑换")
		end
	else
		道具格子=玩家数据[id].角色:取道具格子()
		if 道具格子==0 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		if 商品数量 > 0 then
			if 商品名称=="待补充" then
			else
				玩家数据[id].道具:给予道具(id,商品名称,商品数量,nil,"商店")
			end
			购买成功 = true
		else
			常规提示(id,"你的"..商品类型.."不足无法兑换")
		end
	end
	if 购买成功 then
		local 总价格=商品数量*商品单价
		玩家数据[id].角色:扣除积分(总价格,商品类型)
	end
end
function 场景类_对话商业栏:购买商品秘制积分(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	local 购买成功 = false
	local 道具格子=0
	if 玩家数据[id].角色.秘制积分< 商品数量*商品单价 then
		商品数量 = qz(玩家数据[id].角色.秘制积分/商品单价)
	end
	if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then
		if 商品数量 > 0 then
			if 玩家数据[id].角色:取空道具格子数量() < 商品数量 then
				商品数量 = 玩家数据[id].角色:取空道具格子数量()
			end
			if 商品数量 > 0 then
				for n=1,商品数量 do
					道具格子=玩家数据[id].角色:取道具格子()
					if 道具格子==0 then
						常规提示(id,"您的道具栏物品已经满啦")
						return 0
					end
					if 商品名称=="待补充" then
					else
						玩家数据[id].道具:给予道具(id,商品名称,nil,商品附加+0,"商店")
					end
				end
				购买成功 = true
			else
				常规提示(id,"您的道具栏物品已经满啦")
			end
		else
			常规提示(id,"你的积分不足无法兑换")
		end
	else
		道具格子=玩家数据[id].角色:取道具格子()
		if 道具格子==0 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		if 商品数量 > 0 then
			if 商品名称=="待补充" then
			else
				玩家数据[id].道具:给予道具(id,商品名称,商品数量,商品附加+0,"商店")
			end
			购买成功 = true
		else
			常规提示(id,"你的积分不足无法兑换")
		end
	end
	if 购买成功 then
		local 总价格=商品数量*商品单价
		玩家数据[id].角色.秘制积分 = 玩家数据[id].角色.秘制积分 -  总价格
		发送数据(玩家数据[id].连接id,38,{内容="#W您花费#R"..总价格.."#W秘制积分购买了#R"..商品数量.."#W个#R"..商品名称,频道="xx"})
		-- 使用正确的道具刷新方法，发送完整的道具数据
		玩家数据[id].道具:索要道具(玩家数据[id].连接id,id)
	end
end
function 场景类_对话商业栏:购买商品门贡法宝(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	商品数量=1
	local 购买成功 = false
	if 玩家数据[id].角色.门贡< 1*商品单价 then
		常规提示(id,"你的门派贡献不足无法兑换")
		return
	end
	玩家数据[id].道具:给予法宝(id,商品名称)
	购买成功 = true
	if 购买成功 then
		local 总价格=1*商品单价
		玩家数据[id].角色.门贡 = 玩家数据[id].角色.门贡 -   总价格
		发送数据(玩家数据[id].连接id,38,{内容="#W您花费#R"..总价格.."#W门派贡献购买了#R"..商品数量.."#W个#R"..商品名称,频道="xx"})
		-- 使用正确的道具刷新方法，发送完整的道具数据
		玩家数据[id].道具:索要道具(玩家数据[id].连接id,id)
	end
end
function 场景类_对话商业栏:治疗召唤兽气血(连接id,id)
	local 气血=玩家数据[id].召唤兽:取气血差()
	local 魔法=玩家数据[id].召唤兽:取魔法差()
	local 银子=math.floor(气血*0.5+魔法)
	if 玩家数据[id].角色.银子<银子 then
		发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
		return
	end
	玩家数据[id].角色:扣除银子(银子,0,0,"治疗召唤兽气血魔法",1)
	for n=1,#玩家数据[id].召唤兽.数据 do
		玩家数据[id].召唤兽.数据[n].气血=玩家数据[id].召唤兽.数据[n].最大气血
		玩家数据[id].召唤兽.数据[n].魔法=玩家数据[id].召唤兽.数据[n].最大魔法
	end
	if 玩家数据[id].角色.参战信息~=nil and 玩家数据[id].角色.参战宝宝 then
		发送数据(玩家数据[id].连接id,20.1,{气血=玩家数据[id].角色.参战宝宝.最大气血,魔法=玩家数据[id].角色.参战宝宝.最大魔法})
	end
	发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("收您%s两银子，已将您的所有召唤兽气血和魔法全部恢复至最佳状态。",银子)})
end
function 场景类_对话商业栏:治疗召唤兽忠诚(连接id,id)
	local 忠诚=玩家数据[id].召唤兽:取忠诚差()
	local 银子=忠诚*100
	if 玩家数据[id].角色.银子<银子 then
		发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
		return
	end
	玩家数据[id].角色:扣除银子(银子,0,0,"治疗召唤兽忠诚",1)
	for n=1,#玩家数据[id].召唤兽.数据 do
		if 玩家数据[id].召唤兽.数据[n].忠诚<100 then
			玩家数据[id].召唤兽.数据[n].忠诚=100
		end
	end
	发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("收您%s两银子，已将您的所有召唤兽忠诚恢复至最佳状态。",银子)})
end
function 场景类_对话商业栏:治疗召唤兽全体(连接id,id)
	local 忠诚=玩家数据[id].召唤兽:取忠诚差()
	local 银子=忠诚*100
	local 气血=玩家数据[id].召唤兽:取气血差()
	local 魔法=玩家数据[id].召唤兽:取魔法差()
	银子=银子+math.floor(气血*0.5)+魔法
	if 玩家数据[id].角色.银子<银子 then
		发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
		return
	end
	玩家数据[id].角色:扣除银子(银子,0,0,"治疗召唤兽忠诚、气血、魔法",1)
	for n=1,#玩家数据[id].召唤兽.数据 do
		玩家数据[id].召唤兽.数据[n].气血=玩家数据[id].召唤兽.数据[n].最大气血
		玩家数据[id].召唤兽.数据[n].魔法=玩家数据[id].召唤兽.数据[n].最大魔法
		if 玩家数据[id].召唤兽.数据[n].忠诚<100 then
			玩家数据[id].召唤兽.数据[n].忠诚=100
		end
	end
	if 玩家数据[id].角色.参战信息~=nil and 玩家数据[id].角色.参战宝宝 then
		发送数据(玩家数据[id].连接id,20.1,{气血=玩家数据[id].角色.参战宝宝.最大气血,魔法=玩家数据[id].角色.参战宝宝.最大魔法})
	end
	发送数据(连接id,1501,{名称="超级巫医",模型="巫医",对话=format("收您%s两银子，已将您的所有召唤兽气血、魔法、忠诚恢复至最佳状态。",银子)})
end
function 场景类_对话商业栏:购买商品帮贡(商品名称,商品单价,商品数量,商品类型,商品道具,id,商品附加)
	local 购买成功 = false
	local 道具格子=0
	local 当前帮贡 = 玩家数据[id].角色.BG or 0
	local 帮贡单价 = 1  -- 默认帮贡单价
	local 银子单价 = 0   -- 银子单价，帮派药品需要同时扣除银子和帮贡
	local 库存限制 = 999999  -- 默认库存限制

	-- 检查是否是帮派药品商店
	if 玩家数据[id].商店类型 == "帮派药品" then
		-- 对于帮派药品，需要同时扣除银子和帮贡
		-- 格式：药品名称*银子价格*帮贡价格*库存
		for _, 商品 in ipairs(玩家数据[id].商品列表) do
			local 信息 = 分割文本(商品, "*")
			if 信息[1] == 商品名称 then
				银子单价 = tonumber(信息[2]) or 0
				帮贡单价 = tonumber(信息[3]) or 1
				库存限制 = tonumber(信息[4]) or 999999
				break
			end
		end

		local 帮派编号 = 玩家数据[id].角色.BPBH
		-- 检查库存
		if 库存限制 < 商品数量 then
			商品数量 = 库存限制
			if 商品数量 <= 0 then
				常规提示(id,"该商品库存不足")
				return 0
			end
		end

		-- 检查银子是否足够
		if 取银子(id) < 商品数量*银子单价 then
			local 银子可购买数量 = qz(取银子(id)/银子单价)
			if 银子可购买数量 < 商品数量 then
				商品数量 = 银子可购买数量
			end
		end
	else
		-- 对于其他帮贡商品，使用传入的商品单价作为帮贡价格
		帮贡单价 = 商品单价
		库存限制 = tonumber(商品附加) or 999999
	end

	-- 检查帮贡是否足够
	if 当前帮贡 < 商品数量*帮贡单价 then
		商品数量 = qz(当前帮贡/帮贡单价)
	end

	if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then
		if 商品数量 > 0 then
			if 玩家数据[id].角色:取空道具格子数量() < 商品数量 then
				商品数量 = 玩家数据[id].角色:取空道具格子数量()
			end
			if 商品数量 > 0 then
				for n=1,商品数量 do
					道具格子=玩家数据[id].角色:取道具格子()
					if 道具格子==0 then
						常规提示(id,"您的道具栏物品已经满啦")
						return 0
					end
					-- 给予道具
					玩家数据[id].道具:给予道具(id,商品名称,nil,nil,"商店")
				end
				购买成功 = true
			else
				常规提示(id,"您的道具栏物品已经满啦")
			end
		else
			if 玩家数据[id].商店类型 == "帮派药品" then
				常规提示(id,"你的银子或帮贡不足无法购买")
			else
				常规提示(id,"你的帮贡不足无法购买")
			end
		end
	else
		道具格子=玩家数据[id].角色:取道具格子()
		if 道具格子==0 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		if 商品数量 > 0 then
			玩家数据[id].道具:给予道具(id,商品名称,商品数量,nil,"商店")
			购买成功 = true
		else
			if 玩家数据[id].商店类型 == "帮派药品" then
				常规提示(id,"你的银子或帮贡不足无法购买")
			else
				常规提示(id,"你的帮贡不足无法购买")
			end
		end
	end

	if 购买成功 then
		local 帮贡总价格=商品数量*帮贡单价
		local 银子总价格=商品数量*银子单价

		-- 如果是帮派药品商店，先检查并扣除库存
		if 玩家数据[id].商店类型 == "帮派药品" then
			local 帮派编号 = 玩家数据[id].角色.BPBH

			-- 再次检查库存（防止并发购买导致的库存不足）
			local 库存扣除成功 = 商店处理类:扣除帮派药品库存(帮派编号, 商品名称, 商品数量)

			if not 库存扣除成功 then
				常规提示(id,"库存不足，购买失败")
				return 0
			end

			-- 扣除银子
			if 银子总价格 > 0 then
				玩家数据[id].角色:扣除银子(银子总价格,0,0,"购买帮派药品["..商品名称.."]")
			end
		end

		-- 扣除帮贡
		玩家数据[id].角色.BG = 玩家数据[id].角色.BG - 帮贡总价格
		-- 更新帮派数据中的帮贡
		if 玩家数据[id].角色.BPMC ~= "无帮派" and 帮派数据[玩家数据[id].角色.BPBH] then
			帮派数据[玩家数据[id].角色.BPBH].成员数据[id].帮贡.当前 = 玩家数据[id].角色.BG
		end
		-- 发送更新数据
		发送数据(玩家数据[id].连接id,33,{BG=玩家数据[id].角色.BG})
		if 玩家数据[id].商店类型 == "帮派药品" and 银子总价格 > 0 then
			发送数据(玩家数据[id].连接id,3520,{银子=玩家数据[id].角色.银子})

			-- 只显示剩余库存信息
			local 帮派编号 = 玩家数据[id].角色.BPBH
			if 帮派数据[帮派编号] and 帮派数据[帮派编号].药品商店 then
				for _, 药品 in ipairs(帮派数据[帮派编号].药品商店.商品列表) do
					if 药品.名称 == 商品名称 then
						常规提示(id,"#Y"..商品名称.."#W剩余库存：#R"..药品.库存.."#W个")
						break
					end
				end
			end
		else
			-- 对于其他帮贡商品，保持原有提示
			发送数据(玩家数据[id].连接id,38,{内容="#W您花费#R"..帮贡总价格.."#W点帮贡购买了#R"..商品数量.."#W个#R"..商品名称,频道="xx"})
		end
		-- 使用正确的道具刷新方法，发送完整的道具数据
		玩家数据[id].道具:索要道具(玩家数据[id].连接id,id)
	end
end
return 场景类_对话商业栏