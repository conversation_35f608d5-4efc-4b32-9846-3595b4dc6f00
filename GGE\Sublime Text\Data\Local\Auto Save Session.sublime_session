{"file_history": ["/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/商业对话.lua", "/C/Users/<USER>/Desktop/修复备份/4.14/服务端/<PERSON>ript/角色处理类/道具处理类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/商城类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类/物品.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类/技能.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/0.58/客户端/Script/属性控制/队伍.lua", "/D/myfwd/客户端/script/属性控制/队伍.lua", "/D/myfwd/服务端/Script/任务_小九/中秋任务.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/智能缓存系统.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1103.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1512.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/启动流程整合方案.md", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/缓冲.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/物理技能计算.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/无边框启动器.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa29732.30824.rartemp/无边框启动器.lua", "/D/myfwd/客户端/script/战斗类/战斗类.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派迷宫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/6_无名鬼城.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1130.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1123.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/7_八戒悟空.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/3_玄奘的身世.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/星辰挑战.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/队伍处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/游泳活动.lua", "/D/myfwd/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/押镖任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/D/myfwd/服务端/Script/数据中心/物品数据.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/Script/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器3.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/0.58/服务端/<PERSON>ript/角色处理类/成就处理类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/帮派界面.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图坐标类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1044.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/临时任务/首席争霸.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派PK.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派青龙.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/假人玩家.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/摆摊假人数据.lua", "/D/myfwd/客户端/script/显示类/道具详情.lua", "/D/myfwd/服务端/Script/任务_小九/古董商人.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/wg/客户端/客户端/Script/全局/变量1.lua", "/D/wg/客户端/客户端/Script/场景类/生死劫.lua", "/D/wg/客户端/客户端/Script/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/生死劫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/捉鬼任务.lua", "/D/myfwd/服务端/Script/任务_小九/星官.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_封妖.lua", "/D/myfwd/服务端/Script/任务_小九/王婆西瓜.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/乌鸡国.lua", "/D/mymh/客户端/main.lua", "/D/mymh/服务端/Script/系统处理类/ScriptInit.lua", "/D/mymh/客户端/<PERSON><PERSON>t/系统类/人物框.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/服务端/Script/系统处理类/聊天处理类.lua", "/D/mymh/服务端/<PERSON>ript/初始化脚本.lua", "/D/mymh/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/新建文件夹/<PERSON>ript/小九UI/图鉴.lua", "/D/mymh/服务端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/玩家信息.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/帮派商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/助战处理类/MateControl.lua", "/D/myfwd/客户端/Script/数据中心/物品库.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商店处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/彩虹争霸.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/降妖伏魔.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/main.lua", "/D/myfwd/客户端/Script/数据中心/传送表.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗固伤计算.lua", "/D/myfwd/服务端/Script/数据中心/场景NPC.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1226.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/Script/系统处理类/网络处理类.lua", "/D/0.58/服务端/<PERSON>ript/全局函数类/全局循环类.lua", "/D/0.58/服务端/<PERSON>ript/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/Script/数据中心/传送位置.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/传送圈位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/初始.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/C/Users/<USER>/Desktop/修复备份/4.14/服务端/Script/数据中心/宝宝.lua", "/D/08源码/服务端/Script/角色处理类/召唤兽处理.lua", "/D/08源码/服务端/Script/数据库/宠物数据.lua", "/D/2024.07.04团/服务端/Script/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/其他任务/大雁塔怪.lua", "/D/myfwd/客户端/script/网络/数据交换.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1514.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_渡劫剧情.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1204.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/wg/服务端/服务端源码/Script/战斗处理类/创建战斗.lua", "/D/wg/服务端/服务端源码/Script/对话处理类/对话调用/1001.lua", "/D/wg/服务端/服务端源码/Script/战斗处理类/怪物调用/结算处理.lua", "/D/wg/服务端/服务端源码/Script/战斗处理类/怪物属性.lua"], "folder_history": ["/C/Users/<USER>/Desktop/修复备份/4.14/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/4.14/服务端/Script", "/C/Users/<USER>/Desktop/修复备份/4.14/服务端", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/服务端/Script", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/Script", "/D/0.58/服务端/<PERSON><PERSON>t", "/D/wg/客户端/客户端/Script", "/D/新建文件夹/<PERSON><PERSON>t", "/C/Users/<USER>/Desktop/修复备份/6.8/服务端/Script", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端", "/D/08源码/服务端/Script", "/D/2024.07.04团/服务端/Script", "/D/3月最新赛季服互通源码/服务端/Script", "/D/wg/服务端/服务端源码/Script", "/D/3月最新赛季服互通源码/新赛季客户端/Script", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/6.6/服务端/Script", "/D/08源码/服务端/Gateway/Script/ForYourOwnUse", "/C/Users/<USER>/Desktop/修复备份/6.2/服务端/Script", "/D/无双开团/无双服务端----九黎版/Script", "/D/my2/服务端/<PERSON><PERSON>t", "/D/08源码/客户端/script", "/C/Users/<USER>/Desktop/客户端/Script", "/D/0.58/客户端", "/D/数据库版本/客户端早期源码/script", "/C/Users/<USER>/Desktop/修复备份/4.15/服务端/Script", "/D/2024.07.04团/PC客户端/Script", "/D/无双开团/无双客户端/Script", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/服务端/Script", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/5.9/服务端/Script", "/D/3月最新赛季服互通源码/新赛季客户端", "/C/Users/<USER>/Desktop/修复备份/5.6/服务端/Script", "/C/Users/<USER>/Desktop/修复备份/5.6", "/C/Users/<USER>/Desktop/修复备份/5.6/服务端", "/C/Users/<USER>/Desktop/修复备份/5.2 - 副本/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/5.2/服务端/Script", "/D/3月最新赛季服互通源码/服务端", "/D/wg/服务端/服务端源码", "/C/Users/<USER>/Desktop/修复备份/4.14", "/D/参考代码/客户端", "/C/Users/<USER>/Desktop/修复备份/4.15/客户端/Script", "/C/Users/<USER>/Desktop/修复备份/4.15/服务端", "/D/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script", "/E/BaiduNetdiskDownload/【GGE互通MH回合端游精锐西游-附带全套源码-攻略说明】/服务端/服务端源码/Script", "/E/BaiduNetdiskDownload/2月27神来/1.Server/Script", "/D/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端", "/C/Users/<USER>/Desktop/新建文件夹/客户端/Script", "/C/Users/<USER>/Desktop/新建文件夹/客户端/Script/网络", "/C/Users/<USER>/Desktop/新建文件夹/客户端/Script/小九UI/坐骑", "/C/Users/<USER>/Desktop/新建文件夹/客户端/Script/小九UI/召唤兽", "/E/0.55客户端/Script", "/D/0.58/客户端/<PERSON><PERSON>t", "/C/Users/<USER>/Desktop/新建文件夹/服务端/Script", "/C/Users/<USER>/Desktop/备份/客户端/Script", "/C/Users/<USER>/Desktop/服务端/Script", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/客户端源码/Script", "/C/Users/<USER>/Desktop/新建文件夹 (2)/服务端", "/C/Users/<USER>/Desktop/新建文件夹 (2)/服务端/Script", "/C/Users/<USER>/Desktop/最新保留/服务端/Script", "/E/BaiduNetdiskDownload/【GGE互通MH回合端游精锐西游-附带全套源码-攻略说明】/客户端/客户端/Script", "/C/Users/<USER>/Desktop/最新保留/客户端/Script", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/客户端", "/C/Users/<USER>/Desktop/备份/服务端/Script", "/D/新建文件夹/服务端/Script", "/D/08源码", "/E/BaiduNetdiskDownload/【花好月圆】-三经脉版本「源梦屋」最新2023-10-06修复整理版-三经脉-二十二套战斗锦衣-全新商城-全套源码/源码替换/服务端/Script", "/E/BaiduNetdiskDownload/【花好月圆】-三经脉版本「源梦屋」最新2023-10-06修复整理版-三经脉-二十二套战斗锦衣-全新商城-全套源码/源码替换/客户端/Script", "/C/Users/<USER>/Desktop/助战", "/D/08源码/客户端/script/数据中心", "/E/BaiduNetdiskDownload/吊3九黎城/服务端/服务端/Script", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/客户端源码/Script/数据中心", "/C/Users/<USER>/Desktop/新建文件夹/客户端", "/D/梦幻服务端备份/客户端/Script", "/E/0.55客户端", "/D/my2/客户端/script", "/D/jzyu_梦回西游/客户端", "/D/08源码/服务端/Script/战斗处理类", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI", "/D/数据库版本/服务端早期/Script", "/D/jzyu_梦回西游/服务端/服务端/server/Script", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/客户端源码", "/D/jzyu_梦回西游/服务端/服务端/server", "/D/数据库版本/客户端早期源码", "/D/08源码/客户端", "/D/0222-花好(梦回)/花好三端源码+服务端/服务端", "/D/jzyu_梦回西游/客户端/PC客户端源码/客户端源码/Script"], "last_version": 4192, "last_window_id": 742, "log_indexing": false, "next_update_check": 1749959418, "settings": {"new_window_full_screen": false, "new_window_height": 883.0, "new_window_maximized": false, "new_window_position": [-263.0, 55.0], "new_window_settings": {"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], [[["Packages/Lua/ggeserver.sublime-build", ""], ["Packages/Lua/ggeserver.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "SetGGE"], ["Packages/Lua/ggeserver.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "AboutGGE"]], ["Packages/Lua/ggeserver.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "", "selected_items": [["i", "Package Control: Install Package"], ["lsp", "Package Control: List Packages"], ["Package Control: ", "Package Control: Add Channel"], ["install package", "Package Control: Install Package"], ["IN", "Package Control: Install Package"], ["in", "Package Control: Install Package"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 102.0, "history": ["import urllib.request, os, hashlib;", "import urllib.request, os, hashlib; "]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": ["/C/Users/<USER>/Desktop/修复备份/4.14/服务端/<PERSON>ript/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/商业对话.lua", "/D/myfwd/服务端/Script/任务_小九/中秋任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1103.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1512.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/物理技能计算.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派迷宫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/6_无名鬼城.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1130.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1123.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/7_八戒悟空.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/3_玄奘的身世.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/星辰挑战.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/队伍处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/游泳活动.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/押镖任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/D/myfwd/服务端/Script/数据中心/物品数据.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器3.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图坐标类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1044.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/临时任务/首席争霸.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派PK.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派青龙.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/假人玩家.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/摆摊假人数据.lua", "/D/myfwd/服务端/Script/任务_小九/古董商人.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/捉鬼任务.lua", "/D/myfwd/服务端/Script/任务_小九/星官.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_封妖.lua", "/D/myfwd/服务端/Script/任务_小九/王婆西瓜.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/乌鸡国.lua", "/D/mymh/服务端/Script/系统处理类/ScriptInit.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/服务端/Script/系统处理类/聊天处理类.lua", "/D/mymh/服务端/<PERSON>ript/初始化脚本.lua", "/D/mymh/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/帮派商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/助战处理类/MateControl.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商店处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/彩虹争霸.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/降妖伏魔.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗固伤计算.lua", "/D/myfwd/服务端/Script/数据中心/场景NPC.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1226.lua", "/D/myfwd/服务端/Script/数据中心/传送位置.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/传送圈位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/初始.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/其他任务/大雁塔怪.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1514.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_渡劫剧情.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1204.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/游戏活动类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/任务处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/藏宝阁处理类.lua", "/D/myfwd/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/幻域迷宫.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/锻刀村之战.lua", "/D/myfwd/服务端/Script/数据中心/明暗雷怪.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1070.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1028.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/人物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宠物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/调皮的泡泡.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/东海湾小动物.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/古董商人任务.lua", "/D/myfwd/服务端/Script/数据中心/野怪.lua", "/D/myfwd/服务端/<PERSON>ript/工具/新区重置工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/全局数据初始化工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/数据文件修复工具.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/<PERSON>ript/初系统/无边框启动器.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器11.lua", "/D/myfwd/服务端/Script/任务_小九/天降辰星.lua", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/对话处理类/对话调用/1815.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/神兵异兽榜.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/AI战斗.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/三生石.lua", "/D/myfwd/服务端/script/任务_小九/副本任务/三生石", "/D/myfwd/服务端/<PERSON>ript/战斗处理类/天罡星AI.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统/物品掉落控制.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1865.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派玄武.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家数据类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_妖王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/长安保卫战.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话内容.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/皇宫飞贼.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/车迟斗法.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/镖王活动.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/通天河.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/梦魇夜叉.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/门派闯关.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/雁塔地宫.lua", "/D/myfwd/服务端/Script/数据中心/取师门.lua", "/D/myfwd/服务端/Script/任务_小九/二八星宿.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/师门任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_三界悬赏.lua", "/D/myfwd/服务端/Script/任务_小九/天罡星.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/泾河龙王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/地煞星.lua", "/D/myfwd/服务端/Script/数据中心/染色.lua"], "find": {"height": 43.0}, "find_in_files": {"height": 127.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": ["and 名称 ~= \"钨金\" and", "红雪散\""], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "incremental_find": {"height": 31.0}, "input": {"height": 44.0}, "menu_visible": true, "output.exec": {"height": 185.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 277.0}, "pinned_build_system": "Packages/Lua/ggeserver.sublime-build", "replace": {"height": 58.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "服务端\\Script\\战斗处理类\\ScriptInit.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 231.0, "last_filter": "", "selected_items": [], "width": 574.0}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 282.0, "status_bar_visible": true, "template_settings": {}}, "new_window_width": 1521.0}, "windows": [{"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 4251, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[5, 1, "insert", {"characters": "--"}, "AgAAACMOAAAAAAAAJA4AAAAAAAAAAAAAJA4AAAAAAAAlDgAAAAAAAAAAAAA", "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"], [7, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-05-19 23:41:50"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDQtMDYgMDE6MDI6MDc", "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"], [13, 1, "paste", null, "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", "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"], [15, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-05-19 23:42:39"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDUtMTIgMDE6MDg6NDM", "TgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAADkOAAAAAAAAOQ4AAAAAAAAAAAAAAADwvw"], [1, 1, "revert", null, "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", "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"], [2, 1, "revert", null, "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", "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"], [3, 1, "revert", null, "JgAAAAAAAAAAAAAAAAAAAAAAAABAEwAALS0gQEF1dGhvcjog5bCP5Lmd5ZGA5Li2IC0gUVHvvJo1MjY4NDE2Ci0tIEBEYXRlOiAgIDIwMjUtMDMtMTYgMjI6MzE6MDAKLS0gQExhc3QgTW9kaWZpZWQgYnk6ICAg5Lqk5rWBUVHnvqTvvJo4MzQyNDgxOTEKLS0gQExhc3QgTW9kaWZpZWQgdGltZTogMjAyNS0wNS0xOSAyMzo0MjozOQoKX19T5pyN5YqhID0gcmVxdWlyZSgiZ2dlc2VydmVyIikoKQpmZ2Y9IiotKiIKZmdjPSJAK0AiCm1hdGgucmFuZG9tc2VlZCh0b3N0cmluZyhvcy50aW1lKCkpOnJldmVyc2UoKTpzdWIoMSwgNykpCnJlcXVpcmUoImxmcyIpCueoi+W6j+ebruW9lT1sZnMuY3VycmVudGRpcigpLi5bW1xdXQrliJ3lp4vnm67lvZU956iL5bqP55uu5b2VClNlcnZlckRpcmVjdG9yeT3nqIvluo/nm67lvZUKZm9ybWF0ID0gc3RyaW5nLmZvcm1hdApm5Ye95pWwPXJlcXVpcmUoImZmaeWHveaVsCIpLS0yCmZmaSA9IHJlcXVpcmUoImZmaSIpCmZmaS5jZGVmW1sKY29uc3QgY2hhciogeXooY29uc3QgY2hhciAqcyk7CmNvbnN0IGNoYXIqIHFqcW0oY29uc3QgY2hhciAqYik7CnZvaWQqICAgQ3JlYXRlRmlsZUEoY29uc3QgY2hhciosaW50LGludCx2b2lkKixpbnQsaW50LHZvaWQqKTsKYm9vbCAgICBEZXZpY2VJb0NvbnRyb2wodm9pZCosaW50LHZvaWQqLGludCx2b2lkKixpbnQsdm9pZCosdm9pZCopOwpib29sICBDbG9zZUhhbmRsZSh2b2lkKik7Ci8vCmludCAgICAgIE9wZW5DbGlwYm9hcmQodm9pZCopOwp2b2lkKiAgICBHZXRDbGlwYm9hcmREYXRhKHVuc2lnbmVkKTsKaW50ICAgICAgQ2xvc2VDbGlwYm9hcmQoKTsKdm9pZCogICAgR2xvYmFsTG9jayh2b2lkKik7CmludCAgICAgIEdsb2JhbFVubG9jayh2b2lkKik7CnNpemVfdCAgIEdsb2JhbFNpemUodm9pZCopOwovLwppbnQgICBHZXRQcml2YXRlUHJvZmlsZVN0cmluZ0EoY29uc3QgY2hhciosIGNvbnN0IGNoYXIqLCBjb25zdCBjaGFyKiwgY29uc3QgY2hhciosIHVuc2lnbmVkLCBjb25zdCBjaGFyKik7CmJvb2wgIFdyaXRlUHJpdmF0ZVByb2ZpbGVTdHJpbmdBKGNvbnN0IGNoYXIqLCBjb25zdCBjaGFyKiwgY29uc3QgY2hhciosIGNvbnN0IGNoYXIqKTsKCmludCAgIE9wZW5DbGlwYm9hcmQodm9pZCopOwp2b2lkKiAgIEdldENsaXBib2FyZERhdGEodW5zaWduZWQpOwppbnQgICBDbG9zZUNsaXBib2FyZCgpOwp2b2lkKiAgIEdsb2JhbExvY2sodm9pZCopOwppbnQgICBHbG9iYWxVbmxvY2sodm9pZCopOwpzaXplX3QgIEdsb2JhbFNpemUodm9pZCopOwoKaW50ICAgRW1wdHlDbGlwYm9hcmQoKTsKdm9pZCogICBHbG9iYWxBbGxvYyh1bnNpZ25lZCwgdW5zaWduZWQpOwp2b2lkKiAgIEdsb2JhbEZyZWUodm9pZCopOwp2b2lkKiAgIGxzdHJjcHkodm9pZCosY29uc3QgY2hhciopOwp2b2lkKiAgIFNldENsaXBib2FyZERhdGEodW5zaWduZWQsdm9pZCopOwovLwp0eXBlZGVmIHN0cnVjdCB7CnVuc2lnbmVkIGxvbmcgaVsyXTsgLyogbnVtYmVyIG9mIF9iaXRzXyBoYW5kbGVkIG1vZCAyXjY0ICovCnVuc2lnbmVkIGxvbmcgYnVmWzRdOyAvKiBzY3JhdGNoIGJ1ZmZlciAqLwp1bnNpZ25lZCBjaGFyIGluWzY0XTsgLyogaW5wdXQgYnVmZmVyICovCnVuc2lnbmVkIGNoYXIgZGlnZXN0WzE2XTsgLyogYWN0dWFsIGRpZ2VzdCBhZnRlciBNRDVGaW5hbCBjYWxsICovCn0gTUQ1X0NUWDsKdm9pZCBNRDVJbml0KE1ENV9DVFggKik7CnZvaWQgTUQ1VXBkYXRlKE1ENV9DVFggKiwgY29uc3QgY2hhciAqLCB1bnNpZ25lZCBpbnQpOwp2b2lkIE1ENUZpbmFsKE1ENV9DVFggKik7Ci8vCmludCAgIE1lc3NhZ2VCb3hBKHZvaWQgKiwgY29uc3QgY2hhciosIGNvbnN0IGNoYXIqLCBpbnQpOwp2b2lkICBTbGVlcChpbnQpOwppbnQgICBfYWNjZXNzKGNvbnN0IGNoYXIqLCBpbnQpOwpdXQoKLS0g5YWo5bGA5Y+Y6YeP5Yid5aeL5YyWCuiwg+ivleaooeW8jz1fX2dnZS5pc2RlYnVnCumaj+acuuW6j+WIlz0wCgotLSDliJ3lp4vljJbplJnor6/ml6Xlv5fns7vnu5/vvIjlrprkuYnkuLpfR+eahOWFqOWxgOWPmOmHj+ehruS/neaJgOacieaooeWdl+WPr+iuv+mXru+8iQotLSDkvb/nlKhyYXdzZXTnoa7kv53otYvlgLzkuI3kvJrooqvlhYPooajmi6bmiKoKLS1yYXdzZXQoX0csICLplJnor6/ml6Xlv5ciLCB7fSkKcmF3c2V0KF9HLCAi6ZSZ6K+v5pWw55uuIiwgMCkKX19T5pyN5YqhOui+k+WHuigi6ZSZ6K+v5pel5b+X57O757uf5Yid5aeL5YyW5a6M5oiQIikKCl9fQ+WuouaIt+S/oeaBryA9IHt9Cl9RSkJUPSLnu4/lhbjmgIDml6ciCl9fU+acjeWKoTrnva7moIfpopgoX1FKQlQpCuWIneWni+WMluWujOavlSA9IGZhbHNlCui3s+i9rOa1i+ivlSA9IGZhbHNlCui3s+i9rOaXtumXtCA9IDEK5pyN5Yqh56uv5Y+C5pWwID0ge30KCi0tIOWKoOi9veWIneWni+WMluiEmuacrApyZXF1aXJlKCJTY3JpcHQv5Yid5aeL5YyW6ISa5pysIikKCi0tIOWKoOi9veezu+e7n+aooeWdlwrnianlk4HmjonokL3mjqfliLYgPSByZXF1aXJlKCJTY3JpcHQv57O757ufL+eJqeWTgeaOieiQveaOp+WItiIpCgotLSDliqDovb3lhajlsYDmlbDmja7vvIjlh73mlbDlnKjliJ3lp4vljJbohJrmnKzkuK3lrprkuYnvvIkK5Yqg6L295YWo5bGA5pWw5o2uKCkKCi0tIOWIneWni+WMlumanOeijeezu+e7nwpsb2NhbCDpmpznoo3pm4bmiJAgPSByZXF1aXJlKCLlt6Xlhbcu5ri45oiP6Zqc56KN6ZuG5oiQIikK6Zqc56KN6ZuG5oiQLuWIneWni+WMligpCgotLSDlkK/liqjnur/nqIsK57q/56iLID0gcmVxdWlyZSgiU2NyaXB0L+e6v+eoiy/lrprml7blmagiKSgxLCJTY3JpcHQv57q/56iLL+e6v+eoi+W+queOryIpCue6v+eoizIgPSByZXF1aXJlKCJTY3JpcHQv57q/56iLL+WumuaXtuWZqDExIikoMSwiU2NyaXB0L+e6v+eoiy/nur/nqIvlvqrnjq8xMSIpIC0t5YGH5Lq657q/56iL5ZCv5Yqo5LitCue6v+eoizMgPSByZXF1aXJlKCJTY3JpcHQv57q/56iLL+WumuaXtuWZqDMiKSgxLCJTY3JpcHQv57q/56iLL+e6v+eoi+W+queOrzMiKS0t6YGT5YW35pu05paw57q/56iLIOinkuiJsuabtOaWsArnur/nqIs1ID0gcmVxdWlyZSgiU2NyaXB0L+e6v+eoiy/lrprml7blmag1IikoMSwiU2NyaXB0L+e6v+eoiy/nur/nqIvlvqrnjq81IiktLeWcsOWbvue6v+eoiwrnur/nqIs2ID0gcmVxdWlyZSgiU2NyaXB0L+e6v+eoiy/lrprml7blmag2IikoMSwiU2NyaXB0L+e6v+eoiy/nur/nqIvlvqrnjq82IiktLeWLvumtgue0ogoKLS0g5ZCv5Yqo5pyN5Yqh5ZmoCl9fU+acjeWKoTrlkK/liqgo5pyN5Yqh56uv5Y+C5pWwLmlwLOacjeWKoeerr+WPguaVsC7nq6/lj6MpCgotLSDmnI3liqHlmajkuovku7blpITnkIblh73mlbAKZnVuY3Rpb24gX19T5pyN5YqhOuWQr+WKqOaIkOWKnygpCglyZXR1cm4gMAplbmQKCmZ1bmN0aW9uIF9fU+acjeWKoTrov57mjqXov5vlhaUoSUQsSVAsUE9SVCkKCWlmIElQID09IOacjeWKoeerr+WPguaVsC5pcCB0aGVuCgkJX19T5pyN5YqhOui+k+WHuihzdHJpbmcuZm9ybWF0KCfnvZHlhbPlt7Lov57mjqXvvJooJXMpOiVzOiVzJyxJRCwgSVAsUE9SVCkpCgkJX19D5a6i5oi35L+h5oGvW0lEXSA9IHsKCQlJUCA9IElQLAoJCeiupOivgT1vcy50aW1lKCksCgkJUE9SVCA9IFBPUlQKCQl9CgkJaWYgSVA9PeacjeWKoeerr+WPguaVsC5pcCB0aGVuCgkJCV9fQ+WuouaIt+S/oeaBr1tJRF0u572R5YWzPXRydWUKCQkJ5pyN5Yqh56uv5Y+C5pWwLue9keWFs2lkPUlECgkJZW5kCgllbHNlCgkJX19T5pyN5YqhOuaWreW8gOi/nuaOpShJRCkKCWVuZAplbmQKCmZ1bmN0aW9uIF9fU+acjeWKoTrov57mjqXpgIDlh7ooSUQpCglpZiBfX0PlrqLmiLfkv6Hmga9bSURdIHRoZW4KCQlpZiBfX0PlrqLmiLfkv6Hmga9bSURdLue9keWFsyB0aGVuCgkJCV9fU+acjeWKoTrovpPlh7ooc3RyaW5nLmZvcm1hdCgn572R5YWz5a6i5oi36YCA5Ye6KCVzKTolczolcycsIElELF9fQ+WuouaIt+S/oeaBr1tJRF0uSVAsX19D5a6i5oi35L+h5oGvW0lEXS5QT1JUKSkKCQllbmQKCWVuZAplbmQKCmZ1bmN0aW9uIF9fU+acjeWKoTrmlbDmja7liLDovr4oSUQsLi4uKQoJbG9jYWwgYXJnID0gey4uLn0KCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0gdGhlbgoJCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0u572R5YWzIHRoZW4KCQkJaWYgYXJnWzFdID49MTkwOTA5MjE5ICB0aGVuCgkJCQlpZiBfX0PlrqLmiLfkv6Hmga9bSURdLklQID095pyN5Yqh56uv5Y+C5pWwLmlwIHRoZW4KCQkJCQnlsI/npZ7nvZHlhbM65pWw5o2u5aSE55CGKGFyZ1sxXSxhcmdbMl0pCgkJCQllbmQKCQkJZWxzZQoJCQkJ572R57uc5aSE55CG57G7OuaVsOaNruWkhOeQhihhcmdbMV0sYXJnWzJdKQoJCQllbmQKCQllbmQKCWVuZAplbmQKCmZ1bmN0aW9uIF9fU+acjeWKoTrplJnor6/kuovku7YoSUQsRU8sSUUpCglpZiBfX0PlrqLmiLfkv6Hmga9bSURdIHRoZW4KCQlfX1PmnI3liqE66L6T5Ye6KHN0cmluZy5mb3JtYXQoJ+mUmeivr+S6i+S7tiglcyk6JXMsJXM6JXMnLCBJRCxfX+mUmeivr1tFT10gb3IgRU8sX19D5a6i5oi35L+h5oGvW0lEXS5JUCxfX0PlrqLmiLfkv6Hmga9bSURdLlBPUlQpKQoJZW5kCmVuZAoKLS0g5ZCv5Yqo5ri45oiP5rS75YqoCmZ1bmN0aW9uIOWQr+WKqOa0u+WKqCgpCgktLSDlnKjliJ3lp4vljJbohJrmnKzkuK3lt7Lnu4/orr7nva4gUV/kuK3np4vlvIDlhbMgPSB0cnVl77yM6L+Z6YeM5LiN6ZyA6KaB6YeN5aSN6K6+572uCgnkuK3np4vku7vliqE65byA5ZCv5Lit56eL5Lu75YqhKCkKCS0t5Lu75Yqh5aSE55CG57G7OuW5tOWFvea0u+WKqCgpLS3lubTlhb0KCeS7u+WKoeWkhOeQhuexuzrlrp3lm77lsIHlppYoaWQpLS3lsIHlppYKCeS7u+WKoeWkhOeQhuexuzrorr7nva7lpKfpm4HloZTmgKooKQoJ5Lu75Yqh5aSE55CG57G7OuWkj+aXpeazoeazoSgpCgnku7vliqHlpITnkIbnsbs66LCD55qu55qE5rOh5rOhKGlkKQoKZW5kCgotLSDmiafooYzmtLvliqjlkK/liqgK5ZCv5Yqo5rS75YqoKCkAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAAAAAAAAAAAAeQ4AAAAAAAAAAAAAAAAAAAAAAAB5DgAAAAAAAAAAAAAAAAAAAAAAAHkOAAAAAAAAAAAAAA", "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"], [4, 1, "revert", null, "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", "HQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAgBwAAAAAAACAHAAAAAAAAAAAAAAAA8L8"], [1, 1, "revert", null, "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", "FAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAIEAAAAAAAAAgQAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Script/对话处理类/商业对话.lua", "settings": {"buffer_size": 14253, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[1, 1, "revert", null, "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", "AQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"]]}], "build_system": "Packages/Lua/ggeserver.sublime-build", "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], [[["Packages/Lua/ggeserver.sublime-build", ""], ["Packages/Lua/ggeserver.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "SetGGE"], ["Packages/Lua/ggeserver.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "AboutGGE"]], ["Packages/Lua/ggeserver.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "", "selected_items": [["i", "Package Control: Install Package"], ["lsp", "Package Control: List Packages"], ["Package Control: ", "Package Control: Add Channel"], ["install package", "Package Control: Install Package"], ["IN", "Package Control: Install Package"], ["in", "Package Control: Install Package"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 102.0, "history": ["import urllib.request, os, hashlib;", "import urllib.request, os, hashlib; "]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/服务端", "/D/myfwd/服务端/<PERSON><PERSON>t", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/全局函数类", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/商店处理类", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/对话处理类", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/战斗处理类", "/D/myfwd/服务端/Script/数据中心", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/系统处理类", "/D/BaiduNetdiskDownload/飞蛾静脉/gge"], "file_history": ["/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/商业对话.lua", "/D/myfwd/服务端/Script/任务_小九/中秋任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1103.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1512.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/物理技能计算.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派迷宫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/6_无名鬼城.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1130.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1123.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/7_八戒悟空.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/3_玄奘的身世.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/星辰挑战.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/队伍处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/游泳活动.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/押镖任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/D/myfwd/服务端/Script/数据中心/物品数据.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器3.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图坐标类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1044.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/临时任务/首席争霸.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派PK.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派青龙.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/假人玩家.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/摆摊假人数据.lua", "/D/myfwd/服务端/Script/任务_小九/古董商人.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/捉鬼任务.lua", "/D/myfwd/服务端/Script/任务_小九/星官.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_封妖.lua", "/D/myfwd/服务端/Script/任务_小九/王婆西瓜.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/乌鸡国.lua", "/D/mymh/服务端/Script/系统处理类/ScriptInit.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/服务端/Script/系统处理类/聊天处理类.lua", "/D/mymh/服务端/<PERSON>ript/初始化脚本.lua", "/D/mymh/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/帮派商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/助战处理类/MateControl.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商店处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/彩虹争霸.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/降妖伏魔.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗固伤计算.lua", "/D/myfwd/服务端/Script/数据中心/场景NPC.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1226.lua", "/D/myfwd/服务端/Script/数据中心/传送位置.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/传送圈位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/初始.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/其他任务/大雁塔怪.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1514.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_渡劫剧情.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1204.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/游戏活动类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/任务处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/藏宝阁处理类.lua", "/D/myfwd/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/幻域迷宫.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/锻刀村之战.lua", "/D/myfwd/服务端/Script/数据中心/明暗雷怪.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1070.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1028.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/人物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宠物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/调皮的泡泡.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/东海湾小动物.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/古董商人任务.lua", "/D/myfwd/服务端/Script/数据中心/野怪.lua", "/D/myfwd/服务端/<PERSON>ript/工具/新区重置工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/全局数据初始化工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/数据文件修复工具.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/<PERSON>ript/初系统/无边框启动器.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器11.lua", "/D/myfwd/服务端/Script/任务_小九/天降辰星.lua", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/对话处理类/对话调用/1815.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/神兵异兽榜.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/AI战斗.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/三生石.lua", "/D/myfwd/服务端/script/任务_小九/副本任务/三生石", "/D/myfwd/服务端/<PERSON>ript/战斗处理类/天罡星AI.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统/物品掉落控制.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1865.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派玄武.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家数据类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_妖王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/长安保卫战.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话内容.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/皇宫飞贼.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/车迟斗法.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/镖王活动.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/通天河.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/梦魇夜叉.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/门派闯关.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/雁塔地宫.lua", "/D/myfwd/服务端/Script/数据中心/取师门.lua", "/D/myfwd/服务端/Script/任务_小九/二八星宿.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/师门任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_三界悬赏.lua", "/D/myfwd/服务端/Script/任务_小九/天罡星.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/泾河龙王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/地煞星.lua", "/D/myfwd/服务端/Script/数据中心/染色.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/轮回境副本.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/帮派处理.lua"], "find": {"height": 43.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端\\Script\\任务_小九,<project filters>", "D:\\myfwd\\服务端,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\Server,<project filters>", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\客户端,C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\0.543\\服务端", "D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端\\Script", "D:\\【花好月圆】-三经脉版本-助战分角色+VIP礼包+会员卡+剧情活动\\服务端\\Script\\战斗处理类", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\源码备份\\服务端\\Script", "D:\\梦幻服务端\\服务端", "C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script", "D:\\死神互通全套源码\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端", "D:\\梦幻服务端\\服务端\\Script", "for i=2,5  do", "D:\\梦幻服务端\\服务端\\Script", "D:\\花好互通\\fuwudd\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端,D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端", "D:\\新建文件夹 (3)\\服务端源码", "D:\\梦幻服务端\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\数据中心", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\周末玩法", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\角色处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\副本任务", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\副本任务", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script"]}, "find_state": {"case_sensitive": false, "find_history": ["钨金", "or", "中秋", "迷宫", "变化之术", "齐天大圣", "天雷斩", "帮派土地公公", "迷宫", "扣除银子", "帮派迷宫", "跑商", "中秋", "衙门守卫", "阎王大人，任务已经完成了", "你已经击败了我", "饰品颜色", "视频颜色", "这象", "1123", "千年怨鬼", "鬼将", "饰品显示", "110050", "你已经知道了", "他在森罗殿", "pr", "移动坐标刷新", "警告", "pr", "队伍解散时更新", "队伍解散时", "队伍解散", "游泳", "劫镖", "1144", "劫镖", "100035", "劫镖", "取随机数", "劫镖", "指南", "150", "玄冰", "如意宫灯", "镖", "跑镖", "门派", "跑商", "纵地", "迷宫", "成就", "成就处理", "成就", "成就处理", "保存成就", "升级比赛", "副本首通", "副本", "成就", "5135", "首席", "百鬼噬魂", "帮派", "青龙", "无级别限制", "赤明", "书", "150", "书", "假人", "古董商人", "古董", "天罡气", "迷宫", "bcsj", "无级别限制", "躲避减少", "骷髅", "捉鬼", "书铁", "藏宝图", "宝图封妖", "书铁", "猹", "随机", "法防", "调试", "绿芦羹", "检查聊天处理类", "🔥", "退出战斗", "下线", "强制", "踢出", "下", "下线", "踢", "ID特效", "特效ID", "特效id", "给予跑商道具", "聊天", "9000", "不足无法兑换", "你的商店不足", "商品帽子", "@bc", "商品", "pr", "添加ID特效1", "商品帽子", "取商品卖出价格", "商品面粉", "面粉", "网络处理类加载成功", "bc", "巫医", "检查道具", "给予任务道具", "奏折", "殷", "亲笔", "书信", "纸钱", "古董商人活动定时器开始运行", "古董", "广播"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["告辞", "古董货商", "虞悄悄", "", ",法防 = math.floor(等级*7*难度系数*0.8)", ",防御 = math.floor(等级*7*难度系数)", "\t,防御 = math.floor(等级*5*难度系数)\n\t,法防 = math.floor(等级*5*难度系数)", "藏宝图", "", "破碎无双", "\"高级魔兽要诀\"", "", "6100", "2008", "2001", "7500", "1625", "3000", "8020", "1005", ",穿刺等级= 穿刺等级", ",穿刺等级= 计算穿刺等级", ",穿刺等级= 300", "战斗", "速度 = math.floor(sx.属性.速度*难度系数),", "法伤 = math.floor(sx.属性.法伤*难度系数),", "法伤 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.法伤*难度系数),", "伤害 = math.floor(sx.属性.伤害*难度系数),", "物抗=xiulian + 3", "物抗=xiulian + 5 ", "", "伤害=等级*18", "法防=等级*9", "防御=等级*11", ",技能={\"高级感知\",\"高级反震\",\"高级防御\",\"高级反震\",\"高级魔之心\",\"高级法术波动\",\"高级敏捷\",\"高级强力\",\"高级夜战\",\"高级偷袭\",\"高级必杀\"}", ",气血=等级*等级*8", ",速度=等级*6", ",技能={\"高级感知\"}", "", "法伤结果", "临时法伤结果", "1-目标数*0.1", "计算固伤附加属性", "附加属性.灵饰伤害+附加属性.符石伤害+附加属性.拭剑石", "qz(fhz*分灵+修炼差*5+神木符)", ")", "(", "战斗数据.参战单位[编号].拭剑石伤害", "战斗数据:取灵饰属性", "战斗数据:取符石属性", "固定伤害结果", "宝石属性", "", "local 武器伤害 = math.floor(战斗数据.参战单位[编号].装备伤害)", "+ 4000", "local 名称 = \"特赦令牌\"", "高级藏宝图", "local 经验 = 等级 * 取随机数(120, 130)", "local 银子 = 等级 * 取随机数(105, 120)", "local 经验 = 等级 * 取随机数(140, 160)", "特效宝珠\n\t", "\n\t", "特效宝珠", "躲避", ",速度 = math.floor(sx.属性.速度*1.5)", "取随机数(95, 105)", "等级 * 12", "速度=等级*4", ",速度=等级*3.5", ",伤害=等级*15", "取随机数(90,120)", "取随机数(140,145)", "达到", "被封印 >= 3", "铁血", "渡劫", "奖励参数", "防御 = math.floor(等级*13)", "法防 = math.floor(等级*11)", "速度 = qz(sx.属性.速度)", "", "11", "玩家数据[v].道具", "玩家数据", "玩家数据[v].道具:给予道具", "玩家数据[v].道具:", "主线=10", "self:下一页", "self:第一页", "法伤=等级*13", "sx.属性.气血*20", "sx.属性.气血*16", "气血=等级*145", "气血=等级*125", "气血=等级*140", "sx.属性.气血*10", "sx.属性.气血*12", "法伤=等级*9", "“同气”", "“淝水之战”", "“同气”", "self:取角色选择信息(id, 123)", "发送数据(玩家数据[id].连接id, 122", "发送数据(玩家数据[id].连接id, 121)", "self:取角色选择信息(id, 120)", "降妖伏魔:完成", "self", "任务id", "任务数据[任务id]", "local 银子=等级*55+取随机数(200,400)", "local 经验=等级*取随机数(115,125)", "目标1", "指令.目标", "玩家id", "指令.参数", "指令.类型", "战斗数据", "速度=等级*4", "玩家数据[id].道具:给予道具(id,\"九转金丹\",150,nil,nil,\"专用\")", "玩家数据[id].道具:给予道具(id,\"九转金丹\",50,nil,nil,\"专用\")", "", "取门派", "马副将", "--显示血量计算", "#", "属性.伤害"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 4251, "regions": {}, "selection": [[2895, 2897]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 5, 9, 20, 45, 10, 2, 231, 81, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 1882.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Script/对话处理类/商业对话.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 14253, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 6, 5, 17, 59, 45, 8, 21, 165, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 7458.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 31.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 185.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 277.0}, "pinned_build_system": "Packages/Lua/ggeserver.sublime-build", "position": "0,0,1,-32000,-32000,-1,-1,1028,142,94,1524,3a4b8adc443bc94ea49c7d0d2d7e2c32", "project": "开发服务端.sublime-project", "replace": {"height": 58.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "服务端\\Script\\战斗处理类\\ScriptInit.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 231.0, "last_filter": "", "selected_items": [], "width": 574.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 282.0, "status_bar_visible": true, "template_settings": {}, "window_id": 725, "workspace_name": "/D/myfwd/服务端/开发服务端.sublime-workspace"}, {"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 3938, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[1, 1, "revert", null, "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", "JQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"], [2, 1, "revert", null, "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", "JQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"], [5, 1, "insert", {"characters": "01"}, "AwAAAMIBAAAAAAAAwwEAAAAAAAAAAAAAwwEAAAAAAADDAQAAAAAAAAIAAAA2McMBAAAAAAAAxAEAAAAAAAAAAAAA", "JQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAwgEAAAAAAADEAQAAAAAAAAAAAAAAAPC/"], [7, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-06 23:49:06"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDMgMjM6MTY6MDY", "JQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAxAEAAAAAAADEAQAAAAAAAAAAAAAAAPC/"], [3, 1, "left_delete", null, "AQAAACgNAAAAAAAAKA0AAAAAAAAEAAAAICAtLQ", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAACwNAAAAAAAAKA0AAAAAAAAAAAAAAADwvw"], [6, 1, "left_delete", null, "AQAAAEMNAAAAAAAAQw0AAAAAAAAEAAAAIC0tIA", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEcNAAAAAAAAQw0AAAAAAAAAAAAAAADwvw"], [8, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-08 22:01:59"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDYgMjM6NDk6MDY", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEMNAAAAAAAAQw0AAAAAAAAAAAAAAADwvw"], [13, 1, "toggle_comment", {"block": false}, "AgAAAEMNAAAAAAAARg0AAAAAAAAAAAAAKQ0AAAAAAAAsDQAAAAAAAAAAAAA", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAFkNAAAAAAAAKA0AAAAAAAAAAAAAAADwvw"], [16, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-08 22:03:44"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDggMjI6MDE6NTk", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEQNAAAAAAAARA0AAAAAAAAAAAAAAADwvw"], [8, 1, "paste", null, "AgAAAHcDAAAAAAAAfAMAAAAAAAAAAAAAfAMAAAAAAAB8AwAAAAAAAAYAAADnvJPlhrI", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB5AwAAAAAAAHcDAAAAAAAAAAAAAAAA8L8"], [13, 1, "paste", null, "AQAAAH8DAAAAAAAA5AMAAAAAAAAAAAAA", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB/AwAAAAAAAH8DAAAAAAAAAAAAAAAA8L8"], [14, 1, "add_file_header", {"path": "D:\\myfwd\\客户端\\main.lua"}, "AQAAAAAAAAAAAAAAkgAAAAAAAAAAAAAA", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADkAwAAAAAAAOQDAAAAAAAAAAAAAAAA8L8"], [15, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 21:12:17"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMDggMjI6MDM6NDQ", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB2BAAAAAAAAHYEAAAAAAAAAAAAAAAA8L8"], [20, 1, "cut", {"event": {"modifier_keys": {}, "text_point": 1086, "x": 426.5, "y": 159.5}}, "AQAAABEEAAAAAAAAEQQAAAAAAACjAAAA6LWE5rqQ57yT5a2YID0gcmVxdWlyZSgiU2NyaXB0L+i1hOa6kOexuy/mmbrog73nvJPlrZjns7vnu58iKSh7CiAgICDmnIDlpKfnvJPlrZjmlbAgPSAxNTAwLAogICAg5ZCv55So6aKE5Yqg6L29ID0gdHJ1ZSwKICAgIOiwg+ivleaooeW8jyA9IOiwg+ivleaooeW8jyBvciBmYWxzZQp9KQ", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB2BAAAAAAAABEEAAAAAAAAAAAAAAAA8L8"], [46, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 22:01:29"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMTMgMjE6MTI6MTc", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADqBwAAAAAAAOoHAAAAAAAAAAAAAAAA8L8"], [51, 1, "left_delete", null, "AQAAAAkEAAAAAAAACQQAAAAAAAAJAAAA5LyY5YyW54mI", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAMBAAAAAAAAAkEAAAAAAAAAAAAAAAA8L8"], [53, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-06-13 22:05:59"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDYtMTMgMjI6MDE6Mjk", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAJBAAAAAAAAAkEAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Script/显示类/队标_格子.lua", "settings": {"buffer_size": 2307, "encoding": "UTF-8", "line_ending": "Windows"}}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"]], ["Packages/Lua/ggegame.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "in", "selected_items": [["in", "Package Control: Install Package"], ["Terminus: Open Terminal", "Terminus: Toggle Panel"], ["Package Control: Install Package", "Package Control: Install Package"], ["Install Package Control", "Package Control: Install Package"], ["Package Control", "Package Control: Install Package"], ["preferences control", "Preferences: Package Control Settings – <PERSON><PERSON>ult"], ["pac", "Package Control: Install Package"], ["", "AutoFileName: <PERSON><PERSON><PERSON>s"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 510.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/客户端", "/D/myfwd/客户端/<PERSON><PERSON>t", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类"], "file_history": ["/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/商城类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类/物品.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类/技能.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/客户端/script/属性控制/队伍.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/智能缓存系统.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化版无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/缓冲.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/客户端/Script/初系统/无边框启动器.lua", "/D/myfwd/客户端/script/战斗类/战斗类.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/帮派界面.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/script/显示类/道具详情.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/生死劫.lua", "/D/mymh/客户端/<PERSON><PERSON>t/系统类/人物框.lua", "/D/新建文件夹/<PERSON>ript/小九UI/图鉴.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/玩家信息.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/Script/数据中心/物品库.lua", "/D/myfwd/客户端/Script/数据中心/传送表.lua", "/D/myfwd/客户端/script/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/myfwd/客户端/<PERSON>ript/显示类/游戏公告类.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/藏宝阁/藏宝阁类.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/script/全局/变量1.lua", "/D/myfwd/客户端/script/小九UI/商业/摊位购买.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/道具行囊.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/更新内容.lua", "/C/Users/<USER>/Documents/Tencent Files/308537402/FileRecv/更新内容.lua", "/C/Users/<USER>/Desktop/修复备份/5.9/客户端/Script/初系统/缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/更新类/鉴定提示.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/鉴定.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/黑市拍卖.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/客户端/script/小九UI/商业/商店.lua", "/D/myfwd/客户端/Script/数据中心/场景NPC.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青_测试.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/翰墨丹青_示例.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/Script/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗回放集成.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/Script/初系统/Windows_API_无边框启动器.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/简化版无边框启动器.lua", "/D/myfwd/客户端/Script/数据中心/场景.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/小九UI/角色/人物状态栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/修炼升级.lua", "/D/myfwd/客户端/Script/数据中心/梦战造型.lua", "/D/myfwd/客户端/script/小九UI/排行榜.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/家园访问.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/Script/数据中心/头像库.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/myfwd/客户端/Script/数据中心/坐骑库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽属性栏.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/五子棋.lua", "/D/myfwd/客户端/Script/全局/人物.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/小九UI/道具行囊.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/成就系统.lua", "/D/myfwd/客户端/script/小九UI/角色/成就系统.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/猜拳命令类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/长安保卫战.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/快捷技能栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽资质栏.lua", "/D/myfwd/客户端/<PERSON>ript/显示类/道具详情.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/多重对话类/对话栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/动画类.lua", "/D/myfwd/客户端/<PERSON>ript/资源类/动画类_X9.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/丰富文本.lua", "/D/myfwd/客户端/script/小九UI/翰墨丹青.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘 - 副本.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/攻略查看.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/梦幻指引.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/script/战斗类/战斗单位类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/自适应.lua", "/D/myfwd/客户端/<PERSON>ript/数据中心/普通模型库.lua", "/C/Users/<USER>/Desktop/修复备份/5.24/客户端/Script/战斗类/战斗单位类.lua", "/D/myfwd/服务端/地图障碍设置.lua", "/C/Users/<USER>/Desktop/修复备份/5.24/服务端/Script/对话处理类/NPC对话内容.lua", "/D/myfwd/客户端/update.bat", "/C/Users/<USER>/Desktop/修复备份/4.14/客户端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/召唤兽.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/网络/消息.lua", "/D/myfwd/客户端/<PERSON>ript/全局/玩家.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/网络/数据交换.lua", "/D/myfwd/客户端/script/全局/人物.lua", "/C/Users/<USER>/Desktop/修复备份/5.12 - 副本/客户端/Script/全局/玩家.lua", "/D/myfwd/客户端/script/全局/假人.lua", "/D/myfwd/客户端/script/网络/hp.lua", "/D/myfwd/客户端/<PERSON>ript/网络/消息.lua", "/D/myfwd/GGE/Core/Game/gge纹理类.lua", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/D/myfwd/客户端/Script/main.lua", "/D/myfwd/GGE/Core/Game/gge文字类.lua", "/D/myfwd/客户端/script/系统类/丰富文本.lua", "/D/myfwd/客户端/script/资源类/MAP.lua", "/D/myfwd/GGE/Core/Game/ggecommon.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/D/myfwd/客户端/script/资源类/地图类.lua", "/C/Users/<USER>/Desktop/修复备份/5.9/客户端/<PERSON>ript/小九UI/内置管理工具.lua", "/C/Users/<USER>/Desktop/五子棋 - 副本.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友/好友查看.lua", "/D/myfwd/客户端/Script/数据中心/特效库.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/勾魂索一.lua", "/D/myfwd/客户端/script/系统类/小型选项栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框外部.lua", "/D/myfwd/客户端/<PERSON>ript/聊天框系统/聊天框丰富文本.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/队伍/队伍栏.lua"], "find": {"height": 43.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\客户端\\Script,<project filters>", "D:\\mymh\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端", "D:\\myfwd\\客户端\\Script", "D:\\myfwd\\客户端", "D:\\myfwd\\Client", "D:\\myfwd\\Server\\Script,<project filters>,D:\\myfwd\\Client\\Script", "D:\\myfwd\\Server\\Script,<project filters>"]}, "find_state": {"case_sensitive": true, "find_history": ["or", "商城", "if self.师门技能[id] ~= nil and self.师门技能[id].包含技能 ", "获取状态", "缓冲", "检测系统配置", "低配", "print", "龙卷", "龙卷雨击", "法术抖动", "抖动", "尸腐", "延迟", "百鬼噬魂", "[6]", "统计", "帮派", "剧情npc", "渡劫剧情", "生死劫", "1001", "人物框", "9000", "[10]", "观察", "不足无法兑换", "商品帽子", "跑商商品", "跑商武器", "跑商帽子", "跑商面粉", "少女的手镯", "福隆当铺", "346", "当铺", "长安民居2", "1036", "1130", "主线", "考古", "缓存", "经典", "画面风格", "切换", "聊天框", "[2]", "余额", "藏宝阁", "print", "窗口恢复正常", "txk", "全局商城购买状态", "商城", "道具行囊", "剧情技能", "标题", "[2]", "[0]", "[1]", "道具行囊", "鉴定提示", "gb", "专用提示", "恭喜！", "古董", "古荡", "妙手", "变化之术", "古董", "变化之术", "古董拍卖", "股东拍卖", "自动", "[1]", "鉴定装备", "你的商店不足", "货商", "苏大娘", "奸商", "AI Assistant", "添加地图", "添加npc", "慧静", "正在启动", "已经扫描", "战神山", "pr", "gge研究", "[210]", "人物排行", "帮派", "人物属性", "免资材", "免紫菜", "丫鬟", "佣人", "[8]", "访问玩家", "1028", "小龙女", "进阶龙鲤", "print", "pr", "非跑商商品", "提示", "无法出售", "字体素材", "[3]", "操作说明", "[4]", " [4]", "进度", "提交作品", "进度", "[5]", "查看说明", "进度", "原字", "当前字体", "当前", "普通字体", "操作说明", "[7]", "[6]", "引导模式", "进度", "已加锁，无法使用"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["虞悄悄", "--print", "item.wdf", "zts:显示(self.x+243+100,self.y+68,打造说明[self.分类标识][self.功能标识])", "zts:显示(self.x+177+100,self.y+68,\"需要材料：\")", "zts:显示(self.x+177+144,self.y+68,\"需要材料：\")", "zts:显示(self.x+243+144,self.y+68,打造说明[self.分类标识][self.功能标识])", "战斗类", "玩家", "武器", "行为", "玩家", "资源", "common/zuoqiyws.wdf", "特效宝珠", "shape.wdf", "shape.wd5", "shape.wd4", "祥瑞坐骑.wdf", "shape.wdf", "common/shape.wdf", "", "pgs_1", "shape.wdf", "pgs_1", "shape.wdf", "", "y", "资源", "（过去）", "（未来）", "", "diedai.wdf", "", "资源", "\"wzife.wdf\"", "资源", "载入('wzife.wdf',\"网易WDF动画\",0X140BBA9),"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 3938, "regions": {}, "selection": [[1033, 1033]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 6, 5, 17, 59, 44, 9, 143, 146, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 858.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Script/显示类/队标_格子.lua", "selected": true, "semi_transient": true, "settings": {"buffer_size": 2307, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 6, 14, 14, 7, 18, 2, 134, 123, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 31.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.Terminus": {"height": 150.0}, "output.exec": {"height": 117.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "position": "0,0,2,-32000,-32000,-1,-1,1003,1031,69,2413,3a4b8adc443bc94ea49c7d0d2d7e2c32", "project": "开发客户端.sublime-project", "replace": {"height": 58.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["战斗命令", "Client\\Script\\战斗类\\战斗命令类.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 282.0, "status_bar_visible": true, "template_settings": {}, "window_id": 739, "workspace_name": "/D/myfwd/客户端/开发客户端.sublime-workspace"}], "workspaces": {"recent_workspaces": ["/D/myfwd/客户端/开发客户端.sublime-workspace", "/D/myfwd/服务端/开发服务端.sublime-workspace", "/D/mymh/客户端/开发客户端.sublime-workspace", "/D/mymh/服务端/开发服务端.sublime-workspace", "/D/2024.07.04团/服务端/开发服务端.sublime-workspace", "/D/3月最新赛季服互通源码/服务端/开发服务端.sublime-workspace", "/D/3月最新赛季服互通源码/新赛季客户端/开发梦幻西游.sublime-workspace", "/D/wg/服务端/服务端源码/游戏模板.sublime-workspace", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/客户端源码/开发梦幻西游.sublime-workspace", "/D/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/开发服务端.sublime-workspace", "/E/BaiduNetdiskDownload/2月27神来/1.Server/开发服务端.sublime-workspace", "/E/BaiduNetdiskDownload/2月27神来/2.客户端/开发梦幻西游.sublime-workspace", "/D/my2/服务端/服务端.sublime-workspace", "/E/BaiduNetdiskDownload/【GGE互通MH回合端游精锐西游-附带全套源码-攻略说明】/服务端/服务端源码/开发服务端.sublime-workspace", "/D/无双开团/无双客户端/开发梦幻西游.sublime-workspace", "/D/wg/客户端/客户端/游戏模板.sublime-workspace", "/E/0.55客户端/开发客户端.sublime-workspace", "/D/08源码/服务端/服务端.sublime-workspace", "/D/jzyu_梦回西游/服务端/服务端/server/开发服务端.sublime-workspace", "/D/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/开发梦幻西游.sublime-workspace", "/D/BaiduNetdiskDownload/2008/客户端/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/GGE/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/服务端/开发服务端.sublime-workspace"]}}